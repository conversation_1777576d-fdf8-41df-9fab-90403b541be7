%% vim: set filetype=erlang : -*- erlang -*-

{erl_opts, [
            {platform_define, "^[0-9]+", namespaced_types},
            {platform_define, "^20", unicode_supported},
            warnings_as_errors,
            warn_export_all,
            warn_untyped_record
           ]}.

{xref_checks, [
               fail_on_warning,
               undefined_function_calls
              ]}.

{cover_enabled, true}.

{edoc_opts, [
             {doclet, edown_doclet},
             {app_default, "http://www.erlang.org/doc/man"},
             {dialyzer_specs, all},
             {report_missing_type, true},
             {report_type_mismatch, true},
             {pretty_print, erl_pp},
             {preprocess, true}
            ]}.
{validate_app_modules, true}.

{ct_opts, [{dir, "ct"}]}.

{git_vsn, [{env_key, git_vsn},
           {describe_opt, "--tags --abbrev=10"},
           {separate, true}]}.

{escript_name, bbmustache}.
{escript_incl_apps, [getopt]}.
{escript_comment, "%% https://github.com/soranoba/bbmustache \n"}.

{profiles, [{test, [{erl_opts, [export_all]},
                    {deps,
                     [
                      {jsone, "1.4.6"},
                      {mustache_spec, ".*", {git, "git://github.com/soranoba/spec.git", {tag, "v1.2.1-erl"}}}
                     ]},
                    {plugins, [rebar3_raw_deps]}
                   ]},
            {dev, [{erl_opts, [{d, bbmustache_escriptize}]},
                   {deps,
                    [
                     {getopt, "1.0.1"}
                    ]},
                    {plugins, [rebar3_git_vsn]},
                    {provider_hooks, [{post, [{compile, git_vsn}]}]}
                  ]},
            {doc, [{deps,
                    [
                     {edown, ".*", {git, "git://github.com/uwiger/edown.git", {tag, "0.8.3"}}}
                    ]}
                  ]},
            {bench, [{deps,
                      [
                       {mustache, ".*", {git, "git://github.com/mojombo/mustache.erl", {tag, "v0.1.1"}}}
                      ]}
                    ]}
           ]}.

import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions } from '@headlessui/react'
import { CheckIcon, ChevronDownIcon } from '@heroicons/react/20/solid'
import clsx from 'clsx'
import { useState } from 'react'

const options = [
  { id: 1, name: 'Interest To Banks' },
  { id: 2, name: 'Loans and advances from normal deposits (excluding leasing income)' },
  { id: 3, name: 'Loans and advances from dedicated lines of credit (including leasing income)' },
  { id: 4, name: 'From banks and other financial institutions (including OMO deposits)' },
  { id: 5, name: 'Securities' },
  { id: 6, name: 'Deposits' },
  { id: 7, name: 'Time' },
  { id: 8, name: 'Loans' },
  { id: 9, name: 'Deposits Foreign/Local' },
  { id: 10, name: 'Commissions, fees and service charges (local currency transactions)' },
  { id: 11, name: 'Foreign Exchange' },
  { id: 12, name: 'Trading Income' },
  { id: 13, name: 'Occupancy' },
  { id: 14, name: 'Equipment' },
  { id: 15, name: 'Depreciation' },
  { id: 16, name: 'Education and Training' },
  { id: 17, name: 'Audit, Legal and Professional Fees' },
  { id: 18, name: 'Insurance' },
  { id: 19, name: 'Management Fees' },
  { id: 20, name: 'Donations' }
]

export default function Example() {
  const [query, setQuery] = useState('')
  const [selected, setSelected] = useState(options[0])

  const filteredOptions =
    query === ''
      ? options
      : options.filter((option) => {
          return option.name.toLowerCase().includes(query.toLowerCase())
        })

  return (
    <div className="mx-auto h-screen w-52 pt-20">
      <Combobox value={selected} onChange={(value) => setSelected(value)} onClose={() => setQuery('')}>
        <div className="relative">
          <ComboboxInput
            className={clsx(
              'w-full rounded-lg border-none bg-white/5 py-1.5 pr-8 pl-3 text-sm/6 text-white',
              'focus:outline-none data-[focus]:outline-2 data-[focus]:-outline-offset-2 data-[focus]:outline-white/25'
            )}
            displayValue={(option) => option?.name}
            onChange={(event) => setQuery(event.target.value)}
          />
          <ComboboxButton className="group absolute inset-y-0 right-0 px-2.5">
            <ChevronDownIcon className="size-4 fill-white/60 group-data-[hover]:fill-white" />
          </ComboboxButton>
        </div>

        <ComboboxOptions
          anchor="bottom"
          transition
          className={clsx(
            'w-[var(--input-width)] rounded-xl border border-white/5 bg-white/5 p-1 [--anchor-gap:var(--spacing-1)] empty:invisible',
            'transition duration-100 ease-in data-[leave]:data-[closed]:opacity-0'
          )}
        >
          {filteredOptions.map((option) => (
            <ComboboxOption
              key={option.id}
              value={option}
              className="group flex cursor-default items-center gap-2 rounded-lg py-1.5 px-3 select-none data-[focus]:bg-white/10"
            >
              <CheckIcon className="invisible size-4 fill-white group-data-[selected]:visible" />
              <div className="text-sm/6 text-white">{option.name}</div>
            </ComboboxOption>
          ))}
        </ComboboxOptions>
      </Combobox>
    </div>
  )
}

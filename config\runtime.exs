import Config

# config/runtime.exs is executed for all environments, including
# during releases. It is executed after compilation and before the
# system starts, so it is typically used to load production configuration
# and secrets from environment variables or elsewhere. Do not define
# any compile-time configuration in here, as it won't be applied.
# The block below contains prod specific runtime configuration.

# ## Using releases
#
# If you use `mix release`, you need to explicitly enable the server
# by passing the PHX_SERVER=true when you start it:
#
#     PHX_SERVER=true bin/mis_reports start
#
# Alternatively, you can use `mix phx.gen.release` to generate a `bin/server`
# script that automatically sets the env var above.
# if System.get_env("PHX_SERVER") do
#   config :mis_reports, MisReportsWeb.Endpoint, server: true
# end

if config_env() == :prod do
  # database_url =
  #   System.get_env("DATABASE_URL") ||
  #     raise """
  #     environment variable DATABASE_URL is missing.
  #     For example: ecto://USER:PASS@HOST/DATABASE
  #     """

  # maybe_ipv6 = if System.get_env("ECTO_IPV6"), do: [:inet6], else: []

  # Configure your database
  config :mis_reports, MisReports.Repo,
    adapter: Ecto.Adapters.Tds,
    database: "mis_reports",
    username: "prudential",
    password: "P@ssw0rd",
    hostname: "00172PRUDUATDBv",
    port: "1433",
    pool_size: 100,
    timeout: 920_000,
    pool_timeout: 920_000,
    queue_target: 5_000,             # Handles connection queueing
    queue_interval: 5_000,           # Controls queue check frequency
    idle_timeout: 30_000,            # Closes idle connections gracefully
    backoff: [
    type: :rand_exp,               # Changed from :exponential to :rand_exp
    min: 1_000,                    # 1 second minimum
    max: 30_000                    # 30 seconds maximum
    ]

  # Configure your Endpoint
  config :mis_reports, MisReportsWeb.Endpoint,
    http: [
      port: 4000,
      protocol_options: [
        idle_timeout: 180_000
      ]
    ],
    server: true,
    url: [
      host: "*************",
      port: 4002
    ],
    check_origin: String.split("https://*************:4002,http://*************:4000", ","),
    https: [
      ip: {0, 0, 0, 0},
      port: 4002,
      protocol_options: [
        idle_timeout: 180_000
      ],
      cipher_suite: :compatible,
      certfile: "priv/cert/selfsigned.pem",
      keyfile: "priv/cert/selfsigned_key.pem"
    ],
    secret_key_base: "hXMnKJe3PgfTJ9RNWsxF9o5MmJyftaT8oTv8Bl8Zi/KZ1dfx6tBrWG4Vh+3aDOCR"

  # Configures the Scheduler
  config :mis_reports, MisReports.Scheduler,
    overlap: false,
    timeout: 3_620_000,
    timezone: "Africa/Cairo",
    jobs: [
      # file_uploads: [
      #   schedule: {:extended, "*/10"},
      #   # schedule: "@weekly",
      #   task: {MisReports.Workers.Jobs.ProcessUpload, :perform, []}
      # ],
      # gen_quarterly_publication: [
      #   schedule: {:extended, "*/55"},
      #   # schedule: "@weekly",
      #   task: {MisReports.Workers.Jobs.GenQuarterlyPublication, :perform, []}
      # ],
      #  save_quarterly_publication: [
      #   schedule: {:extended, "*/55"},
      #   # schedule: "@weekly",
      #   task: {MisReports.Workers.Jobs.SaveQuarterlyPublication, :perform, []}
      # ],
      # save_monthly_prudential: [
      #   schedule: {:extended, "*/60"},
      #   # schedule: "@weekly",
      #   task: {MisReports.Workers.Jobs.SaveMonthyPrudential, :perform, []}
      # ],
      # populate_monthly_prudential: [
      #   schedule: {:extended, "*/55"},
      #   # schedule: "@weekly",
      #   task: {MisReports.Workers.Jobs.GenMonthlyPrudential, :perform, []}
      # ],
      # save_quarterly_cmmp: [
      #   schedule: {:extended, "*/60"},
      #   # schedule: "@weekly",
      #   task: {MisReports.Workers.Jobs.Cmmp, :perform, []}
      # ],
      # populate_quarterly_cmmp: [
      #   schedule: {:extended, "*/55"},
      #   # schedule: "@weekly",
      #   task: {MisReports.Workers.Jobs.GenQuarterlyCmmp, :perform, []}
      # ]
    ]



  # The secret key base is used to sign/encrypt cookies and other secrets.
  # A default value is used in config/dev.exs and config/test.exs but you
  # want to use a different value for prod and you most likely don't want
  # to check this value into version control, so we use an environment
  # variable instead.
  # secret_key_base =
  #   System.get_env("SECRET_KEY_BASE") ||
  #     raise """
  #     environment variable SECRET_KEY_BASE is missing.
  #     You can generate one by calling: mix phx.gen.secret
  #     """

  # host = System.get_env("PHX_HOST") || "example.com"
  # port = String.to_integer(System.get_env("PORT") || "4000")

  # config :mis_reports, MisReportsWeb.Endpoint,
  #   url: [host: host, port: 443, scheme: "https"],
  #   http: [
  #     # Enable IPv6 and bind on all interfaces.
  #     # Set it to  {0, 0, 0, 0, 0, 0, 0, 1} for local network only access.
  #     # See the documentation on https://hexdocs.pm/plug_cowboy/Plug.Cowboy.html
  #     # for details about using IPv6 vs IPv4 and loopback vs public addresses.
  #     ip: {0, 0, 0, 0, 0, 0, 0, 0},
  #     port: port
  #   ],
  #   secret_key_base: secret_key_base


  # ## Configuring the mailer
  #
  # In production you need to configure the mailer to use a different adapter.
  # Also, you may need to configure the Swoosh API client of your choice if you
  # are not using SMTP. Here is an example of the configuration:
  #
  #     config :mis_reports, MisReports.Mailer,
  #       adapter: Swoosh.Adapters.Mailgun,
  #       api_key: System.get_env("MAILGUN_API_KEY"),
  #       domain: System.get_env("MAILGUN_DOMAIN")
  #
  # For this example you need include a HTTP client required by Swoosh API client.
  # Swoosh supports Hackney and Finch out of the box:
  #
  #     config :swoosh, :api_client, Swoosh.ApiClient.Hackney
  #
  # See https://hexdocs.pm/swoosh/Swoosh.html#module-installation for details.
end

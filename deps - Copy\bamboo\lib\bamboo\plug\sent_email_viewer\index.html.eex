<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Sent Email</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/4.0.0/normalize.css" charset="utf-8">
    <style>
      body {
        min-width: 800px;
        font-size: 15px;
        font-family: sans-serif;
      }

      iframe {
        width: 100%;
        border: none;
      }

      .truncate {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .container {
        display: flex;
        flex-direction: row;
      }

      .email-sidebar {
        display: flex;
        flex-direction: column;
        padding: 25px 35px;;
        width: 40%;
        max-width: 400px;
      }

      .selected-email {
        background-color: #2D9EB9;
        -webkit-box-shadow: 0px 5px 17px 1px rgba(0,0,0,0.21);
        -moz-box-shadow: 0px 5px 17px 1px rgba(0,0,0,0.21);
        box-shadow: 0px 5px 17px 1px rgba(0,0,0,0.21);
      }

      .selected-email .email-summary-subject,
      .selected-email .email-summary-recipients {
        color: #fff;
      }

      .selected-email .email-summary-body-excerpt {
        color: #CDF0F8;
      }

      .email-summary {
        display: flex;
        flex-direction: column;
        text-decoration: none;
        color: #000;
        padding: 15px 25px;
        border-radius: 5px;
      }

      .email-summary:hover {
        -webkit-box-shadow: 0px 5px 17px 1px rgba(0,0,0,0.11);
        -moz-box-shadow: 0px 5px 17px 1px rgba(0,0,0,0.11);
        box-shadow: 0px 5px 17px 1px rgba(0,0,0,0.11);
      }

      .email-summary-subject {
        font-weight: 700;
        font-size: 15px;
        padding: 5px 0px;
      }

      .email-summary-recipients {
        font-size: 15px;
        padding-bottom: 5px;
      }

      .email-summary-body-excerpt {
        color: #777;
        max-height: 40px;
        font-size: 14px;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .email-detail-pane {
        width: 100%;
        padding: 35px;
        padding-left: 0;
        display: flex;
        flex-direction: column;
      }

      .email-detail-hero {
        display: flex;
        flex-direction: column;
        padding-left: 20px;
      }

      .email-detail-subject {
        font-weight: bold;
        font-size: 22px;
        padding: 10px 0;
      }

      .email-attachment-icon::after {
        content: '📎';
        filter: grayscale(100%);
      }

      .email-detail-attachments {
        display: flex;
        align-items: center;
        margin: 0.2em 0;
      }

      .email-detail-attachments-icon-container {
        margin: 0 0.25em 0 0;
      }

      .email-detail-attachments ul {
        display: flex;
        flex-wrap: wrap;
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .email-detail-attachments ul li {
        padding-right: 0.25em;
      }

      .email-detail-attachments ul li:not(:last-of-type)::after {
        content: ',';
      }

      .email-detail-attachments ul li a {
        font-weight: bold;
      }

      .email-detail-bodies-container {
        border: 1px solid #eee;
        border-radius: 5px;
        padding: 20px;
        margin-top: 30px;
      }

      .email-detail-body-label {
        padding: 0;
        margin: 0 0 12px 0;
        font-size: 15px;
        font-weight: 700;
      }

      .email-detail-recipients,
      .email-detail-headers,
      .email-detail-attachments {
        color: #aaa;
      }

      .email-detail-recipients strong,
      .email-detail-headers strong,
      .email-detail-attachments a {
        color: #555;
      }

      .email-detail-body {
        margin: 15px 0 25px 0;
      }
    </style>
  </head>

  <body>
    <main class="container">
      <aside class="email-sidebar">
        <%= for email <- @emails do %>
          <a
            class="email-summary <%= Bamboo.SentEmailViewerPlug.Helper.selected_email_class(email, @selected_email) %>"
            href="<%= "#{@base_path}/#{Bamboo.SentEmail.get_id(email)}" %>">
            <span class="email-summary-subject truncate" title="<%= Bamboo.SentEmailViewerPlug.Helper.format_subject(email.subject) %>">
              <%= Bamboo.SentEmailViewerPlug.Helper.format_subject(email.subject) %>
            </span>
            <span class="email-summary-recipients truncate">
              <%= Bamboo.SentEmailViewerPlug.Helper.format_email_address(email.from) %>
              to <%= Bamboo.SentEmailViewerPlug.Helper.email_addresses(email) %>
            </span>
            <span class="email-summary-body-excerpt">
              <%= if Enum.count(email.attachments) > 0 do %><span class="email-attachment-icon"></span><% end %>
              <%= Bamboo.SentEmailViewerPlug.Helper.format_text_body(email.text_body) %>
            </span>
          </a>
        <% end %>
      </aside>
      <section class="email-detail-pane">
        <section class="email-detail-hero">
          <span class="email-detail-subject"><%= Bamboo.SentEmailViewerPlug.Helper.format_subject(@selected_email.subject) %></span>
          <span class="email-detail-recipients">
            From <strong><%= Bamboo.SentEmailViewerPlug.Helper.format_email_address(@selected_email.from) %></strong>
            to <strong><%= Bamboo.SentEmailViewerPlug.Helper.email_addresses(@selected_email) %></strong>
          </span>
          <span class="email-detail-headers">
            <%= for {header, value} <- @selected_email.headers do %>
              <div class="email-detail-header">
                <%= header %> <strong><%= Bamboo.SentEmailViewerPlug.Helper.format_headers(value) %></strong>
              </div>
            <% end %>
          </span>
          <% attachment_count = Enum.count(@selected_email.attachments) %>
          <%= if  attachment_count > 0 do %>
            <span class="email-detail-attachments">
              <span class="email-detail-attachments-icon-container"><span class="email-attachment-icon"></span></span>
              <ul>
              <%= for index <- 0..attachment_count - 1 do %>
                <%
                  attachment = Enum.at(@selected_email.attachments, index)
                  href = "#{@base_path}/#{Bamboo.SentEmail.get_id(@selected_email)}/attachments/#{index}"
                %>
                <li><a href="<%= href %>"><%= Bamboo.SentEmailViewerPlug.Helper.format_attachment(attachment) %></a></li>
              <% end %>
              </ul>
            </span>
          <% end %>
        </section>

        <section class="email-detail-bodies-container">
          <h3 class="email-detail-body-label">HTML body</h3>
          <p class="email-detail-body">
            <script>
            function adjustFrameHeight(iframe) {
              var contentWindow = iframe.contentWindow;
              var height = (contentWindow.outerHeight)
                ? contentWindow.outerHeight
                : contentWindow.screen.availHeight;
              iframe.style.height = height + "px";
            }
            </script>
            <iframe onload="adjustFrameHeight(this)" src="<%= "#{@base_path}/#{Bamboo.SentEmail.get_id(@selected_email)}/html" %>"></iframe>
          </p>

          <h3 class="email-detail-body-label">Text Body</h3>
          <pre class="email-detail-body"><%= Bamboo.SentEmailViewerPlug.Helper.format_text_body(@selected_email.text_body) %></pre>
        </section>
      </section>
    </main>
  </body>
</html>

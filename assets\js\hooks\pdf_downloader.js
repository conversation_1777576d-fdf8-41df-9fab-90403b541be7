const PdfDownloader = {
    mounted() {
      const button = this.el.querySelector("#download-btn");
      button.addEventListener("click", () => {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'pt', 'a4');
        const content = this.el.querySelector("#pdf-content");
        doc.html(content, {
          callback: function (doc) {
            doc.save("Quarterly_Report.pdf");
          },
          x: 10,
          y: 10,
          width: 190,
        });
      });
    }
  };
  
  export default PdfDownloader;
  
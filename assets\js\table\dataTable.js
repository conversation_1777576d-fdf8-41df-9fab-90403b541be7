$.fn.dataTable.ext.buttons.back = {
    text:      '<i class="icon-arrow-left13"></i> Back',
    titleAttr: 'Go Back',
    action: function ( e, dt, node, config ) {
        parent.history.back();
        return false;
    }
};

let dtTable = {
    initTransMgt() {
        var dt = $('#dt-trans-mgt').DataTable({
            "processing": true,
            'language': {
                'loadingRecords': '&nbsp;',
                processing: '<i class="icon-spinner2 spinner"></i>'
            },
            "serverSide": true,
            "paging": true,
            'ajax': {
                "type": "POST",
                "url": '/bop/transactions',
                "data": {
                    // "searchTerm": $('#searchTerm').val(),
                    "trsRef": $('#trsRef').val(),
                    "from": $('#from').val(),
                    "to": $('#to').val(),
                    "trn_code": $('#trn_code').val(),
                    "status": $('#status').val(),
                    "auth_level": $("#auth_level").val(),
                    "_csrf_token": $("#csrf").val()
                }
            },
            "columns": [
                {
                    "className": 'details-control',
                    "orderable": false,
                    "data": null,
                    "defaultContent": ''
                },
                { "data": "branch_code" },
                { "data": "trn_ref" },
                { "data": "fgn_pay_cur" },
                { 
                    "data": "fgn_pay_amt",
                    "className": "text-right text-semibold",
                    "render": function ( data, type, row ) {
                        if(data) {
                            return formartAmount(data.replace(/\,/g, ''));
                        } else{
                            0.00
                        }
                    }
                },
                { 
                    "data": "fgn_pay_rat",
                    "className": "text-right text-semibold",
                    "render": function ( data, type, row ) {
                        if(data) {
                            return formartAmount(data.replace(/\,/g, ''));
                        } else{
                            0.00
                        }
                    }
                },
                { 
                    "data": "loc_pay_amt",
                    "className": "text-right text-semibold",
                    "render": function ( data, type, row ) {
                        if(data) {
                            return formartAmount(data.replace(/\,/g, ''));
                        } else{
                            0.00
                        }
                    }
                },
                { "data": "status" },
                {
                    "className": 'options text-center',
                    "orderable": false,
                    "data": null,
                    "render": function (data, type, row) {                        
                        let $actions = $('#dt-action-list')
                                .find('a').each(function (index, action) {
                                    var attr = $(action).attr('data-id');
                                    $(action).attr('id', $(action).attr('id') + '-' + row.id);
                                    
                                    if (typeof attr !== 'undefined' && attr !== false) {
                                        $(action).attr('data-id', row.id);
                                    } else {
                                        $(action).attr('phx-value-id', row.id);
                                    }
                                })
                                .prop('outerHTML');
                        return $actions;
                    }
                }
            ],
            "lengthMenu": [[10, 25, 50, 100, 1000], [10, 25, 50, 100, 1000]],
            "order": [[1, 'asc']]
        });

        return dt;
    },
    transMgtSearch(table) {
        table.on('preXhr.dt', function (e, settings, data) {
            data.trsRef = $("#trsRef").val();
            data.from = $("#from").val();
            data.to = $("#to").val();
            data.status = $("#status").val();
            data.trn_code = $("#trn_code").val();
            data._csrf_token = $("#csrf").val();
            data.auth_level = $("#auth-level").val();
        });
        table.draw();
    },
    initGeneral() {
        var dt = $('#dt-table').DataTable({
            dom: 'lBfrtip<"actions">',
            buttons: [
                {
                    extend:    'copyHtml5',
                    text:      'Copy <i class="fa fa-files-o"></i>',
                    titleAttr: 'Copy'
                },
                {
                    extend:    'excelHtml5',
                    text:      'Excel <i class="fa fa-file-excel-o"></i>',
                    titleAttr: 'Excel'
                },
                {
                    extend:    'csvHtml5',
                    text:      'CSV <i class="fa fa-file-text-o"></i>',
                    titleAttr: 'CSV'
                },
                {
                    extend:    'pdfHtml5',
                    text:      'PDF <i class="fa fa-file-pdf-o"></i>',
                    titleAttr: 'PDF'
                },
                {
                    extend:    'print',
                    text:      'Print <i class="fa fa-print"></i>',
                    titleAttr: 'Print'
                },
                    'back'
            ]
        });

        return dt;
    },
    destroy() {
        if ($.fn.DataTable.isDataTable("#dt-table")) {
            $('#dt-table').DataTable().clear().destroy();
        }
    },
    referesh(table) {
        table.ajax.reload();
    }
}

export default dtTable
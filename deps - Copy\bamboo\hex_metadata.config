{<<"app">>,<<"bamboo">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,
 <<"Straightforward, powerful, and adapter based Elixir email library. Works with Mandrill, Mailgun, SendGrid, SparkPost, Postmark, in-memory, and test.">>}.
{<<"elixir">>,<<"~> 1.6">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/bamboo.ex">>,<<"lib/mix">>,
  <<"lib/mix/start_sent_email_viewer_task.ex">>,<<"lib/bamboo">>,
  <<"lib/bamboo/adapter.ex">>,<<"lib/bamboo/api_error.ex">>,
  <<"lib/bamboo/test.ex">>,<<"lib/bamboo/strategies">>,
  <<"lib/bamboo/strategies/deliver_later_strategy.ex">>,
  <<"lib/bamboo/strategies/immediate_delivery_strategy.ex">>,
  <<"lib/bamboo/strategies/task_supervisor_strategy.ex">>,
  <<"lib/bamboo/formatter.ex">>,<<"lib/bamboo/sent_email.ex">>,
  <<"lib/bamboo/adapters">>,<<"lib/bamboo/adapters/adapter_helper.ex">>,
  <<"lib/bamboo/adapters/test_adapter.ex">>,
  <<"lib/bamboo/adapters/local_adapter.ex">>,
  <<"lib/bamboo/adapters/send_grid_helper.ex">>,
  <<"lib/bamboo/adapters/mandrill_helper.ex">>,
  <<"lib/bamboo/adapters/mailgun_adapter.ex">>,
  <<"lib/bamboo/adapters/send_grid_adapter.ex">>,
  <<"lib/bamboo/adapters/mandrill_adapter.ex">>,
  <<"lib/bamboo/adapters/mailgun_helper.ex">>,<<"lib/bamboo/phoenix.ex">>,
  <<"lib/bamboo/mailer.ex">>,<<"lib/bamboo/attachment.ex">>,
  <<"lib/bamboo/view.ex">>,<<"lib/bamboo/plug">>,
  <<"lib/bamboo/plug/sent_email_viewer">>,
  <<"lib/bamboo/plug/sent_email_viewer/email_not_found.html.eex">>,
  <<"lib/bamboo/plug/sent_email_viewer/index.html.eex">>,
  <<"lib/bamboo/plug/sent_email_viewer/no_emails.html.eex">>,
  <<"lib/bamboo/plug/sent_email_viewer/email_preview_plug.ex">>,
  <<"lib/bamboo/plug/sent_email_viewer/helper.ex">>,
  <<"lib/bamboo/plug/sent_email_api">>,
  <<"lib/bamboo/plug/sent_email_api/sent_email_api_plug.ex">>,
  <<"lib/bamboo/email.ex">>,<<"lib/bamboo/template.ex">>,<<".formatter.exs">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE.txt">>,<<"CHANGELOG.md">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/thoughtbot/bamboo">>}]}.
{<<"name">>,<<"bamboo">>}.
{<<"requirements">>,
 [[{<<"app">>,<<"plug">>},
   {<<"name">>,<<"plug">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.0">>}],
  [{<<"app">>,<<"mime">>},
   {<<"name">>,<<"mime">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.4">>}],
  [{<<"app">>,<<"phoenix">>},
   {<<"name">>,<<"phoenix">>},
   {<<"optional">>,true},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.1">>}],
  [{<<"app">>,<<"hackney">>},
   {<<"name">>,<<"hackney">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<">= 1.15.2">>}],
  [{<<"app">>,<<"jason">>},
   {<<"name">>,<<"jason">>},
   {<<"optional">>,true},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.0">>}]]}.
{<<"version">>,<<"1.7.1">>}.

{erl_first_files, ["src/certifi_pt.erl"]}.

{erl_opts, [deterministic  %% Added since OTP 20
           ,{platform_define, "^2", 'OTP_20_AND_ABOVE'}
           ]}.

{project_plugins, [rebar3_hex, rebar3_ex_doc]}.

{hex, [
    {doc, #{provider => ex_doc}}
]}.

{ex_doc, [
    {extras, [
          {"README.md", #{title => "Overview"}},
          {"LICENSE", #{title => "License"}}
    ]},
    {main, "README.md"},
    {source_url, "https://github.com/certifi/erlang-certifi"},
    {assets, "assets"},
    {api_reference, true}
]}.
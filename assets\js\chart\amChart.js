let chart = {
    init(data) {
        AmCharts.makeChart("chartdiv", {
            "type": "serial",
            "theme": "light",
            "dataDateFormat": "YYYY-MM-DD",
            "precision": 0,
            "valueAxes": [{
                "id": "v1",
                "title": "Total Transactions",
                "position": "left",
                "autoGridCount": true
            }],
            "graphs": [{
                "id": "g3",
                "valueAxis": "v1",
                "lineColor": "#1b5e20",
                "fillColors": "#1b5e20",
                "fillAlphas": 1,
                "type": "column",
                "title": "SUBMITTED",
                "valueField": "submitted",
                "clustered": true,
                "columnWidth": 0.7,
                "numberFormat": "#.",
                "legendValueText": "[[value]]",
                "balloonText": "[[title]]<br /><b style='font-size: 130%'>[[value]]</b>"
            }, {
                "id": "g4",
                "valueAxis": "v1",
                "lineColor": "#b71c1c",
                "fillColors": "#b71c1c",
                "fillAlphas": 1,
                "type": "column",
                "title": "FAILED",
                "valueField": "failed",
                "clustered": false,
                "columnWidth": 0.5,
                "legendValueText": "[[value]]",
                "balloonText": "[[title]]<br /><b style='font-size: 130%'>[[value]]</b>"
            },
            {
                "id": "g2",
                "valueAxis": "v2",
                "bullet": "round",
                "bulletBorderAlpha": 1,
                "bulletColor": "#4a148c",
                "bulletSize": 5,
                "hideBulletsCount": 50,
                "lineThickness": 2,
                "lineColor": "#4a148c",
                "type": "smoothedLine",
                "dashLength": 5,
                "title": "PENDING",
                "useLineColorForBulletBorder": true,
                "valueField": "pending",
                "balloonText": "[[title]]<br /><b style='font-size: 130%'>[[value]]</b>"
            },{
                "id": "g1",
                "valueAxis": "v2",
                "bullet": "round",
                "bulletBorderAlpha": 1,
                "bulletColor": "#d50000 ",
                "bulletSize": 5,
                "hideBulletsCount": 50,
                "lineThickness": 2,
                "lineColor": "#d50000 ",
                "type": "smoothedLine",
                "dashLength": 5,
                "title": "APPROVALS",
                "useLineColorForBulletBorder": true,
                "valueField": "approvals",
                "balloonText": "[[title]]<br /><b style='font-size: 130%'>[[value]]</b>"
            }],
            "chartCursor": {
                "pan": true,
                "valueLineEnabled": true,
                "valueLineBalloonEnabled": true,
                "cursorAlpha": 0,
                "valueLineAlpha": 0.2
            },
            "categoryField": "date",
            "categoryAxis": {
                "parseDates": true,
                "dashLength": 1,
                "minorGridEnabled": true
            },
            "legend": {
                "useGraphSettings": false,
                "position": "top"
            },
            "balloon": {
                "borderThickness": 1,
                "shadowAlpha": 0
            },
            "numberFormatter": {
                "numberFormat": "#."
            },
            "export": {
            "enabled": false
            },
            "dataProvider": data
        });
    },
    container(width, height){
        var initialized = false;
        var chartDiv = document.getElementById('chartdiv');
        if(chartDiv){
            chartDiv.style.width = width;
            chartDiv.style.height = height;
            initialized = true;
        }
        return initialized;
    },
    defaultData(){
        var data = []
        var currYear = new Date().getFullYear()
        var currMonth = new Date().getMonth()
        var days = new Date(currYear, currMonth, 0).getDate();
        for(var day = 0; day < days; day++) {
            var row = {
                approvals: 0,
                date: new Date(currYear, currMonth, day + 1),
                failed: 0,
                pending: 0,
                submitted: 0
            }
            data.push(row);
        }
        return data;
    },
    destroy(){
        AmCharts.clear()
    }  
}

// "dataLoader": {
//     "url": "data.json",
//     "format": "json"
//   }

export default chart
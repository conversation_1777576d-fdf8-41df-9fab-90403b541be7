<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Sent Email</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/4.0.0/normalize.css" charset="utf-8">
    <style>
      html {
        height: 100%;
      }

      body {
        min-height: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 100%;
        font-family: sans-serif;
      }

      .notice {
        width: 80%;
        max-width: 500px;
        padding: 40px 50px;
        background-color: #f8f8f8;
        border-radius: 12px;
      }

      .main-message {
        text-align: center;
        margin-bottom: 40px;
      }

      h1 {
        font-weight: 700;
        font-size: 18px;
      }

      li {
        margin-bottom: 20px;
        line-height: 27px;
        color: #666;
      }
    </style>
  </head>

  <body>
    <main class="notice">
      <h1 class="main-message">No emails sent. Maybe because:</h1>
      <ul>
        <li>
          <strong>Not using Bamboo.LocalAdapter.</strong> Make sure you have
          your mailer configured to use Bamboo.LocalAdapter or the
          emails will not show up here.
        </li>

        <li>
          <strong>You recently restarted your server.</strong> The viewer
          does not persist emails so whenever you restart the server the list
          will be cleared.
        </li>

        <li>
          <strong>You're running multiple OS-level BEAM processes.</strong>
          Sent emails are stored in memory. If you have more than one OS-level
          process running, like "mix phx.server" in one terminal and "iex -S mix"
          in another, emails sent in the latter won't be visible in the former.
        </li>
      </ul>
    </main>
  </body>
</html>

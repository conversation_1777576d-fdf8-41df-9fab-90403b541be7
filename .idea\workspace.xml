<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0d60caca-2858-4e45-99a0-079b7db6d534" name="Changes" comment="resolved conflict after merging teddy 02-18-2025">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/dev.exs" beforeDir="false" afterPath="$PROJECT_DIR$/config/dev.exs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports/utilities.ex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports/utilities.ex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports/workers/jobs/initiate_task.ex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports/workers/jobs/initiate_task.ex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports/workflow.ex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports/workflow.ex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/live/adjustments_live/adjustments_component.ex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/live/adjustments_live/adjustments_component.ex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/live/adjustments_live/index.ex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/live/adjustments_live/index.ex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/live/adjustments_live/index.html.heex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/live/adjustments_live/index.html.heex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/live/components/tabs_for_adj.ex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/live/components/tabs_for_adj.ex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/live/insertion_live/index.ex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/live/insertion_live/index.ex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/live/insertion_live/index.html.heex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/live/insertion_live/index.html.heex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/live/insertion_live/insertion_component.ex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/live/insertion_live/insertion_component.ex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/templates/adjustments/approve_new.html.heex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/templates/adjustments/approve_new.html.heex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/mis_reports_web/templates/insertion/approve_new.html.heex" beforeDir="false" afterPath="$PROJECT_DIR$/lib/mis_reports_web/templates/insertion/approve_new.html.heex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/priv/static/assets/app.css" beforeDir="false" afterPath="$PROJECT_DIR$/priv/static/assets/app.css" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="CLEAR_OUTPUT_DIRECTORY" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="clement" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2qKgFkHQ9HtNFWD3LM05ajhHn40" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Elixir Mix.Unnamed.executor&quot;: &quot;Debug&quot;,
    &quot;Elixir Mix.phx.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;clement&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/RiderProjects/mis_reports&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;com.intellij.ide.scratch.ScratchImplUtil$2/New Scratch File&quot;: [
      &quot;JSON&quot;,
      &quot;Elixir&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\RiderProjects" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="phx" type="MixRunConfigurationType" factoryName="Elixir Mix">
      <mix>
        <argument>phx.server</argument>
      </mix>
      <module-filters inherit-application-module-filters="true" />
      <method v="2">
        <option name="LaunchBrowser.Before.Run" url="http://localhost:4000/" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.22562.145" />
        <option value="bundled-js-predefined-d6986cc7102b-deb605915726-JavaScript-IU-243.22562.145" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0d60caca-2858-4e45-99a0-079b7db6d534" name="Changes" comment="" />
      <created>1734416582761</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1734416582761</updated>
      <workItem from="1734416586018" duration="148000" />
      <workItem from="1734416743508" duration="305000" />
      <workItem from="1734417059925" duration="3008000" />
      <workItem from="1734529362991" duration="17000" />
      <workItem from="1734529421213" duration="431000" />
      <workItem from="1734595374898" duration="3019000" />
      <workItem from="1735196756012" duration="1857000" />
      <workItem from="1735198713785" duration="1956000" />
      <workItem from="1735201145802" duration="11290000" />
      <workItem from="1735282485452" duration="15619000" />
      <workItem from="1735822286724" duration="8234000" />
      <workItem from="1736259023457" duration="9061000" />
      <workItem from="1736512297822" duration="2395000" />
      <workItem from="1736522931938" duration="3013000" />
      <workItem from="1736757345857" duration="1902000" />
      <workItem from="1736776377843" duration="709000" />
      <workItem from="1736866954642" duration="681000" />
      <workItem from="1737638479127" duration="658000" />
      <workItem from="1737639210291" duration="1409000" />
      <workItem from="1737721580727" duration="210000" />
      <workItem from="1738747587421" duration="3000" />
      <workItem from="1739862537027" duration="451000" />
      <workItem from="1739864370012" duration="80000" />
      <workItem from="1739870020388" duration="1677000" />
      <workItem from="1739883959349" duration="11843000" />
      <workItem from="1740041278900" duration="148000" />
      <workItem from="1740041610734" duration="247000" />
      <workItem from="1740042030409" duration="329000" />
      <workItem from="1740042453732" duration="472000" />
      <workItem from="1740044293583" duration="479000" />
      <workItem from="1740047077001" duration="9974000" />
      <workItem from="1740389076642" duration="1033000" />
<<<<<<< HEAD
=======
      <workItem from="1741349562085" duration="171000" />
      <workItem from="1741626280033" duration="2796000" />
      <workItem from="1741851829924" duration="330000" />
      <workItem from="1742311859742" duration="190000" />
      <workItem from="1742313428415" duration="62000" />
      <workItem from="1742565555344" duration="353000" />
      <workItem from="1742569435481" duration="1372000" />
      <workItem from="1742827722410" duration="1138000" />
      <workItem from="1742835988284" duration="958000" />
    </task>
    <task id="LOCAL-00001" summary="merged with Teddy branch">
      <option name="closed" value="true" />
      <created>1734529848193</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1734529848193</updated>
    </task>
    <task id="LOCAL-00002" summary="Added change password logic to outside page">
      <option name="closed" value="true" />
      <created>1735207074043</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1735207074043</updated>
    </task>
    <task id="LOCAL-00003" summary="added source_data.ex">
      <option name="closed" value="true" />
      <created>1735283415486</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1735283415486</updated>
    </task>
    <task id="LOCAL-00004" summary="got sh27 changes and index">
      <option name="closed" value="true" />
      <created>1735283961415</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1735283961415</updated>
    </task>
    <task id="LOCAL-00005" summary="got weekly report changes from litaba">
      <option name="closed" value="true" />
      <created>1735286142572</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1735286142574</updated>
    </task>
    <task id="LOCAL-00006" summary="added weekly and sh27a1">
      <option name="closed" value="true" />
      <created>1735306543768</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1735306543768</updated>
    </task>
    <task id="LOCAL-00007" summary="updated sh27a1 and added gmo_tpins">
      <option name="closed" value="true" />
      <created>1735891658605</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1735891658605</updated>
    </task>
    <task id="LOCAL-00008" summary="pulled litaba's updates">
      <option name="closed" value="true" />
      <created>1735904565764</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1735904565764</updated>
    </task>
    <task id="LOCAL-00009" summary="put back my files">
      <option name="closed" value="true" />
      <created>1735904599340</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1735904599340</updated>
    </task>
    <task id="LOCAL-00010" summary="resolved merge conflict after merging with weekly report">
      <option name="closed" value="true" />
      <created>1736340728596</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1736340728596</updated>
    </task>
    <task id="LOCAL-00011" summary="pull teddy">
      <option name="closed" value="true" />
      <created>1736512447835</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1736512447837</updated>
    </task>
    <task id="LOCAL-00012" summary="recovered merge after pulling Teddy">
      <option name="closed" value="true" />
      <created>1736757696885</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1736757696885</updated>
    </task>
    <task id="LOCAL-00013" summary="recovered merge after pulling Teddy 14 Jan">
      <option name="closed" value="true" />
      <created>1736866997675</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1736866997675</updated>
    </task>
    <task id="LOCAL-00014" summary="recovered merge after pulling Teddy 15 Jan">
      <option name="closed" value="true" />
      <created>1736923096015</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1736923096015</updated>
    </task>
    <task id="LOCAL-00015" summary="resolved merge with teddy 23 Jan">
      <option name="closed" value="true" />
      <created>1737638704859</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1737638704859</updated>
    </task>
    <task id="LOCAL-00016" summary="updated code 27a5">
      <option name="closed" value="true" />
      <created>1737639019247</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1737639019247</updated>
    </task>
    <task id="LOCAL-00017" summary="updated code 27a5">
      <option name="closed" value="true" />
      <created>1737639413968</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1737639413968</updated>
    </task>
    <task id="LOCAL-00018" summary="updated code 27 after resolving merge conflict">
      <option name="closed" value="true" />
      <created>1739862828306</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1739862828306</updated>
    </task>
    <task id="LOCAL-00019" summary="resolved conflict after merging teddy 02-18-2025">
      <option name="closed" value="true" />
      <created>1739864424199</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1739864424199</updated>
    </task>
    <task id="LOCAL-00020" summary="resolved conflict after merging teddy 02-18-2025">
      <option name="closed" value="true" />
      <created>1742312056726</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1742312056727</updated>
    </task>
    <option name="localTasksCounter" value="21" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="merged with Teddy branch" />
    <MESSAGE value="Added change password logic to outside page" />
    <MESSAGE value="added source_data.ex" />
    <MESSAGE value="got sh27 changes and index" />
    <MESSAGE value="got weekly report changes from litaba" />
    <MESSAGE value="added weekly and sh27a1" />
    <MESSAGE value="updated sh27a1 and added gmo_tpins" />
    <MESSAGE value="pulled litaba's updates" />
    <MESSAGE value="put back my files" />
    <MESSAGE value="resolved merge conflict after merging with weekly report" />
    <MESSAGE value="pull teddy" />
    <MESSAGE value="recovered merge after pulling Teddy" />
    <MESSAGE value="recovered merge after pulling Teddy 14 Jan" />
    <MESSAGE value="recovered merge after pulling Teddy 15 Jan" />
    <MESSAGE value="resolved merge with teddy 23 Jan" />
    <MESSAGE value="updated code 27a5" />
    <MESSAGE value="updated code 27 after resolving merge conflict" />
    <MESSAGE value="resolved conflict after merging teddy 02-18-2025" />
    <option name="LAST_COMMIT_MESSAGE" value="resolved conflict after merging teddy 02-18-2025" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>
{<<"name">>,<<"bbmustache">>}.
{<<"version">>,<<"1.12.2">>}.
{<<"requirements">>,#{}}.
{<<"app">>,<<"bbmustache">>}.
{<<"maintainers">>,[<<"Hinagiku Soranoba">>]}.
{<<"precompiled">>,false}.
{<<"description">>,
 <<"Binary pattern match Based Mustache template engine for Erlang/OTP">>}.
{<<"files">>,
 [<<"src/bbmustache.app.src">>,<<"LICENSE">>,<<"README.md">>,
  <<"rebar.config">>,<<"rebar.lock">>,<<"src/bbmustache.erl">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/soranoba/bbmustache">>}]}.
{<<"build_tools">>,[<<"rebar3">>]}.

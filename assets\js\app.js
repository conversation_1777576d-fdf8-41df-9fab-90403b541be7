// We import the CSS which is extracted to its own file by esbuild.
// Remove this line if you add a your own CSS build pipeline (e.g postcss).
// import css from "../css/app.css"
// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"
import LiveHooks from "./hooks/live_hooks.js"
import Alpine from 'alpinejs'
import Tom<PERSON>elect from 'tom-select';
import "./clear_comments.js"; // Import the script to clear comments from localStorage
window.Alpine = Alpine
Alpine.start()

import PdfDownloader from "./hooks/pdf_downloader";

// Combine all hooks
let Hooks = { ...LiveHooks };
Hooks.PdfDownloader = PdfDownloader;

// LiveSocket will be created below with proper configuration



// import 'select2';
// import { LiveReact, initLiveReact } from "phoenix_live_react"
// let hooks = { LiveReact }
// import "select2";
// import "select2/dist/css/select2.css";
// import $ from "jquery";
// window.jQuery = $;
// window.$ = $;



// $(document).ready(function() {
//     $('.select2').select2();
//   });


// document.addEventListener('DOMContentLoaded', () => {
//     initializeTomSelect();
//     window.addEventListener('phx:update', initializeTomSelect);
//   });

//   function initializeTomSelect() {
//     new TomSelect('.tom-select', {
//       create: false,
//       sortField: {
//         field: 'text',
//         direction: 'asc'
//       }
//     });
//   }



// document.addEventListener('DOMContentLoaded', function () {
//     new TomSelect('.tom-select', {
//       create: false  // Disable adding new options, keep only searching
//     });
//   });


// window.Alpine = Alpine
// Alpine.start()




let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")
let liveSocket = new LiveSocket("/live", Socket, {hooks: Hooks,
    dom: {
        onBeforeElUpdated(from, to) {
            if (from._x_dataStack) {
                window.Alpine.clone(from, to)
            }
        },
    },
    params: {_csrf_token: csrfToken}, timeout: 60000})

window.addEventListener("phx:toggle-dropdown", (event) => {
    if (event.target.classList.contains('dropdown-toggle')) {
        const elements = document.querySelectorAll('.dropdown-menu-open');
        for (let i = 0; i < elements.length; i++) {
            elements[i].classList.add('transform', 'opacity-0', 'scale-95', 'hidden');
            elements[i].classList.remove('transform', 'opacity-100', 'scale-100', 'dropdown-menu-open');
        }
        event.target.classList.remove('dropdown-toggle');
    } else if (event.target.classList.contains('scale-95')) {
        event.target.classList.remove('transform', 'opacity-0', 'scale-95', 'hidden');
        event.target.classList.add('transform', 'opacity-100', 'scale-100', 'dropdown-menu-open');
        event.target.previousElementSibling.querySelector('button').classList.add('dropdown-toggle');
    } else if (event.target.classList.contains('dropdown-menu-open')) {
        event.target.classList.add('transform', 'opacity-0', 'scale-95', 'hidden');
        event.target.classList.remove('transform', 'opacity-100', 'scale-100', 'dropdown-menu-open');
        event.target.previousElementSibling.querySelector('button').classList.remove('dropdown-toggle');
    }
});

window.addEventListener("phx:toggle-menu-state", (event) => {
    if (event.target.classList.contains('bg-indigo-700')) {
        event.target.classList.remove('bg-indigo-700', 'text-white');
        event.target.classList.add('text-white', 'hover:bg-indigo-500', 'hover:bg-opacity-75');
    } else {
        event.target.classList.add('bg-indigo-700', 'text-white');
        event.target.classList.remove('text-white', 'hover:bg-indigo-500', 'hover:bg-opacity-75');
    }
});

window.addEventListener("phx:table-dropdown", (event) => {
    if (event.target.classList.contains('dropdown-toggle')) {
        const elements = document.querySelectorAll('.dropdown-menu-open');
        for (let i = 0; i < elements.length; i++) {
            elements[i].classList.add('hidden');
            elements[i].classList.remove('dropdown-menu-open');
        }
        event.target.classList.remove('dropdown-toggle');
    } else if (event.target.classList.contains('hidden')) {
        event.target.classList.remove('hidden');
        event.target.classList.add('dropdown-menu-open');
        event.target.previousElementSibling.classList.add('dropdown-toggle');
    } else if (event.target.classList.contains('dropdown-menu-open')) {
        event.target.classList.add('hidden');
        event.target.classList.remove('dropdown-menu-open');
        event.target.previousElementSibling.classList.remove('dropdown-toggle');
    }
});

// Show progress bar on live navigation and form submits
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
window.addEventListener("phx:page-loading-start", info => topbar.show())
window.addEventListener("phx:page-loading-stop", info => topbar.hide())

// Connect if there are any LiveViews on the page
liveSocket.connect()
// initLiveReact() - Commented out as it's not defined

// Expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)
// The latency simulator is enabled for the duration of the browser session.
// Call disableLatencySim() to disable:
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

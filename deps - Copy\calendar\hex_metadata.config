{<<"app">>,<<"calendar">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,
 <<"Calendar is a datetime library for Elixir.\n\nTimezone support via its sister package `tzdata`.\n\nSafe parsing and formatting of standard formats (ISO, RFC, etc.), strftime formatting. Interoperability with erlang style\ndatetime tuples. Extendable through protocols.">>}.
{<<"elixir">>,<<"~> 1.3">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/calendar">>,<<"lib/calendar.ex">>,
  <<"lib/calendar/ambiguous_date_time.ex">>,<<"lib/calendar/date">>,
  <<"lib/calendar/date.ex">>,<<"lib/calendar/date/format.ex">>,
  <<"lib/calendar/date/parse.ex">>,<<"lib/calendar/date_time">>,
  <<"lib/calendar/date_time.ex">>,<<"lib/calendar/date_time/format.ex">>,
  <<"lib/calendar/date_time/interval.ex">>,
  <<"lib/calendar/date_time/parse.ex">>,
  <<"lib/calendar/date_time/tz_period.ex">>,
  <<"lib/calendar/naive_date_time">>,<<"lib/calendar/naive_date_time.ex">>,
  <<"lib/calendar/naive_date_time/format.ex">>,
  <<"lib/calendar/naive_date_time/interval.ex">>,
  <<"lib/calendar/naive_date_time/parse.ex">>,
  <<"lib/calendar/parse_util.ex">>,<<"lib/calendar/strftime.ex">>,
  <<"lib/calendar/time">>,<<"lib/calendar/time.ex">>,
  <<"lib/calendar/time/format.ex">>,<<"lib/calendar/time/parse.ex">>,
  <<"lib/calendar/time_zone_data.ex">>,<<"priv">>,<<"priv/tmp_downloads">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE">>,<<"CHANGELOG.md">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/lau/calendar">>}]}.
{<<"maintainers">>,[<<"Lau Taarnskov">>]}.
{<<"name">>,<<"calendar">>}.
{<<"requirements">>,
 [[{<<"app">>,<<"tzdata">>},
   {<<"name">>,<<"tzdata">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 0.5.20 or ~> 0.1.201603 or ~> 1.0">>}]]}.
{<<"version">>,<<"0.17.6">>}.

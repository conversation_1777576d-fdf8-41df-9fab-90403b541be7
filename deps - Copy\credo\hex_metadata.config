{<<"links">>,
 [{<<"Changelog">>,
   <<"https://github.com/rrrene/credo/blob/master/CHANGELOG.md">>},
  {<<"GitHub">>,<<"https://github.com/rrrene/credo">>}]}.
{<<"name">>,<<"credo">>}.
{<<"version">>,<<"1.7.9">>}.
{<<"description">>,
 <<"A static code analysis tool with a focus on code consistency and teaching.">>}.
{<<"elixir">>,<<">= 1.13.0">>}.
{<<"app">>,<<"credo">>}.
{<<"files">>,
 [<<".credo.exs">>,<<".template.check.ex">>,<<".template.debug.html">>,
  <<"CHANGELOG.md">>,<<"lib">>,<<"lib/mix">>,<<"lib/mix/tasks">>,
  <<"lib/mix/tasks/credo.gen.check.ex">>,
  <<"lib/mix/tasks/credo.gen.config.ex">>,<<"lib/mix/tasks/credo.ex">>,
  <<"lib/credo.ex">>,<<"lib/credo">>,<<"lib/credo/priority.ex">>,
  <<"lib/credo/build_info.ex">>,<<"lib/credo/cli.ex">>,
  <<"lib/credo/exs_loader.ex">>,<<"lib/credo/cli">>,
  <<"lib/credo/cli/command">>,<<"lib/credo/cli/command/help.ex">>,
  <<"lib/credo/cli/command/version.ex">>,<<"lib/credo/cli/command/explain">>,
  <<"lib/credo/cli/command/explain/output">>,
  <<"lib/credo/cli/command/explain/output/default.ex">>,
  <<"lib/credo/cli/command/explain/output/json.ex">>,
  <<"lib/credo/cli/command/explain/explain_output.ex">>,
  <<"lib/credo/cli/command/explain/explain_command.ex">>,
  <<"lib/credo/cli/command/gen.check.ex">>,<<"lib/credo/cli/command/list">>,
  <<"lib/credo/cli/command/list/list_command.ex">>,
  <<"lib/credo/cli/command/list/output">>,
  <<"lib/credo/cli/command/list/output/default.ex">>,
  <<"lib/credo/cli/command/list/output/flycheck.ex">>,
  <<"lib/credo/cli/command/list/output/sarif.ex">>,
  <<"lib/credo/cli/command/list/output/json.ex">>,
  <<"lib/credo/cli/command/list/output/oneline.ex">>,
  <<"lib/credo/cli/command/list/list_output.ex">>,
  <<"lib/credo/cli/command/info">>,<<"lib/credo/cli/command/info/output">>,
  <<"lib/credo/cli/command/info/output/default.ex">>,
  <<"lib/credo/cli/command/info/output/json.ex">>,
  <<"lib/credo/cli/command/info/info_command.ex">>,
  <<"lib/credo/cli/command/info/info_output.ex">>,
  <<"lib/credo/cli/command/categories">>,
  <<"lib/credo/cli/command/categories/categories_command.ex">>,
  <<"lib/credo/cli/command/categories/categories_output.ex">>,
  <<"lib/credo/cli/command/categories/output">>,
  <<"lib/credo/cli/command/categories/output/default.ex">>,
  <<"lib/credo/cli/command/categories/output/json.ex">>,
  <<"lib/credo/cli/command/suggest">>,
  <<"lib/credo/cli/command/suggest/suggest_command.ex">>,
  <<"lib/credo/cli/command/suggest/output">>,
  <<"lib/credo/cli/command/suggest/output/default.ex">>,
  <<"lib/credo/cli/command/suggest/output/flycheck.ex">>,
  <<"lib/credo/cli/command/suggest/output/sarif.ex">>,
  <<"lib/credo/cli/command/suggest/output/json.ex">>,
  <<"lib/credo/cli/command/suggest/output/oneline.ex">>,
  <<"lib/credo/cli/command/suggest/suggest_output.ex">>,
  <<"lib/credo/cli/command/diff">>,
  <<"lib/credo/cli/command/diff/diff_summary.ex">>,
  <<"lib/credo/cli/command/diff/output">>,
  <<"lib/credo/cli/command/diff/output/default.ex">>,
  <<"lib/credo/cli/command/diff/output/flycheck.ex">>,
  <<"lib/credo/cli/command/diff/output/json.ex">>,
  <<"lib/credo/cli/command/diff/output/oneline.ex">>,
  <<"lib/credo/cli/command/diff/diff_output.ex">>,
  <<"lib/credo/cli/command/diff/task">>,
  <<"lib/credo/cli/command/diff/task/get_git_diff.ex">>,
  <<"lib/credo/cli/command/diff/task/print_before_info.ex">>,
  <<"lib/credo/cli/command/diff/task/filter_issues.ex">>,
  <<"lib/credo/cli/command/diff/task/filter_issues_for_exit_status.ex">>,
  <<"lib/credo/cli/command/diff/task/print_results_and_summary.ex">>,
  <<"lib/credo/cli/command/diff/diff_command.ex">>,
  <<"lib/credo/cli/command/gen.config.ex">>,
  <<"lib/credo/cli/exit_status.ex">>,<<"lib/credo/cli/filename.ex">>,
  <<"lib/credo/cli/command.ex">>,<<"lib/credo/cli/output">>,
  <<"lib/credo/cli/output/ui.ex">>,<<"lib/credo/cli/output/summary.ex">>,
  <<"lib/credo/cli/output/formatter">>,
  <<"lib/credo/cli/output/formatter/flycheck.ex">>,
  <<"lib/credo/cli/output/formatter/sarif.ex">>,
  <<"lib/credo/cli/output/formatter/json.ex">>,
  <<"lib/credo/cli/output/formatter/oneline.ex">>,
  <<"lib/credo/cli/output/format_delegator.ex">>,
  <<"lib/credo/cli/output/first_run_hint.ex">>,
  <<"lib/credo/cli/output/shell.ex">>,<<"lib/credo/cli/filter.ex">>,
  <<"lib/credo/cli/options.ex">>,<<"lib/credo/cli/output.ex">>,
  <<"lib/credo/cli/switch.ex">>,<<"lib/credo/cli/task">>,
  <<"lib/credo/cli/task/run_checks.ex">>,
  <<"lib/credo/cli/task/load_and_validate_source_files.ex">>,
  <<"lib/credo/cli/task/set_relevant_issues.ex">>,
  <<"lib/credo/cli/task/prepare_checks_to_run.ex">>,
  <<"lib/credo/cli/sorter.ex">>,<<"lib/credo/test">>,
  <<"lib/credo/test/check_runner.ex">>,<<"lib/credo/test/assertions.ex">>,
  <<"lib/credo/test/source_files.ex">>,<<"lib/credo/test/case.ex">>,
  <<"lib/credo/code.ex">>,<<"lib/credo/plugin.ex">>,<<"lib/credo/check">>,
  <<"lib/credo/check/warning">>,
  <<"lib/credo/check/warning/expensive_empty_enum_check.ex">>,
  <<"lib/credo/check/warning/leaky_environment.ex">>,
  <<"lib/credo/check/warning/operation_on_same_values.ex">>,
  <<"lib/credo/check/warning/dbg.ex">>,
  <<"lib/credo/check/warning/raise_inside_rescue.ex">>,
  <<"lib/credo/check/warning/lazy_logging.ex">>,
  <<"lib/credo/check/warning/unused_path_operation.ex">>,
  <<"lib/credo/check/warning/unused_file_operation.ex">>,
  <<"lib/credo/check/warning/unused_string_operation.ex">>,
  <<"lib/credo/check/warning/application_config_in_module_attribute.ex">>,
  <<"lib/credo/check/warning/map_get_unsafe_pass.ex">>,
  <<"lib/credo/check/warning/unsafe_exec.ex">>,
  <<"lib/credo/check/warning/unused_list_operation.ex">>,
  <<"lib/credo/check/warning/mix_env.ex">>,
  <<"lib/credo/check/warning/bool_operation_on_same_values.ex">>,
  <<"lib/credo/check/warning/unused_operation.ex">>,
  <<"lib/credo/check/warning/unused_enum_operation.ex">>,
  <<"lib/credo/check/warning/spec_with_struct.ex">>,
  <<"lib/credo/check/warning/unused_regex_operation.ex">>,
  <<"lib/credo/check/warning/missed_metadata_key_in_logger_config.ex">>,
  <<"lib/credo/check/warning/unused_function_return_helper.ex">>,
  <<"lib/credo/check/warning/unused_keyword_operation.ex">>,
  <<"lib/credo/check/warning/io_inspect.ex">>,
  <<"lib/credo/check/warning/wrong_test_file_extension.ex">>,
  <<"lib/credo/check/warning/unused_tuple_operation.ex">>,
  <<"lib/credo/check/warning/forbidden_module.ex">>,
  <<"lib/credo/check/warning/iex_pry.ex">>,
  <<"lib/credo/check/warning/unsafe_to_atom.ex">>,
  <<"lib/credo/check/warning/operation_with_constant_result.ex">>,
  <<"lib/credo/check/params.ex">>,<<"lib/credo/check/runner.ex">>,
  <<"lib/credo/check/config_comment.ex">>,<<"lib/credo/check/readability">>,
  <<"lib/credo/check/readability/module_doc.ex">>,
  <<"lib/credo/check/readability/prefer_implicit_try.ex">>,
  <<"lib/credo/check/readability/single_pipe.ex">>,
  <<"lib/credo/check/readability/redundant_blank_lines.ex">>,
  <<"lib/credo/check/readability/max_line_length.ex">>,
  <<"lib/credo/check/readability/pipe_into_anonymous_functions.ex">>,
  <<"lib/credo/check/readability/one_pipe_per_line.ex">>,
  <<"lib/credo/check/readability/parentheses_on_zero_arity_defs.ex">>,
  <<"lib/credo/check/readability/single_function_to_block_pipe.ex">>,
  <<"lib/credo/check/readability/strict_module_layout.ex">>,
  <<"lib/credo/check/readability/with_custom_tagged_tuple.ex">>,
  <<"lib/credo/check/readability/trailing_white_space.ex">>,
  <<"lib/credo/check/readability/module_attribute_names.ex">>,
  <<"lib/credo/check/readability/parentheses_in_condition.ex">>,
  <<"lib/credo/check/readability/alias_order.ex">>,
  <<"lib/credo/check/readability/specs.ex">>,
  <<"lib/credo/check/readability/unnecessary_alias_expansion.ex">>,
  <<"lib/credo/check/readability/space_after_commas.ex">>,
  <<"lib/credo/check/readability/function_names.ex">>,
  <<"lib/credo/check/readability/large_numbers.ex">>,
  <<"lib/credo/check/readability/predicate_function_names.ex">>,
  <<"lib/credo/check/readability/variable_names.ex">>,
  <<"lib/credo/check/readability/string_sigils.ex">>,
  <<"lib/credo/check/readability/separate_alias_require.ex">>,
  <<"lib/credo/check/readability/one_arity_function_in_pipe.ex">>,
  <<"lib/credo/check/readability/with_single_clause.ex">>,
  <<"lib/credo/check/readability/block_pipe.ex">>,
  <<"lib/credo/check/readability/trailing_blank_line.ex">>,
  <<"lib/credo/check/readability/alias_as.ex">>,
  <<"lib/credo/check/readability/module_names.ex">>,
  <<"lib/credo/check/readability/semicolons.ex">>,
  <<"lib/credo/check/readability/impl_true.ex">>,
  <<"lib/credo/check/readability/multi_alias.ex">>,
  <<"lib/credo/check/readability/nested_function_calls.ex">>,
  <<"lib/credo/check/readability/prefer_unquoted_atoms.ex">>,
  <<"lib/credo/check/design">>,
  <<"lib/credo/check/design/skip_test_without_comment.ex">>,
  <<"lib/credo/check/design/tag_fixme.ex">>,
  <<"lib/credo/check/design/tag_helper.ex">>,
  <<"lib/credo/check/design/duplicated_code.ex">>,
  <<"lib/credo/check/design/alias_usage.ex">>,
  <<"lib/credo/check/design/tag_todo.ex">>,<<"lib/credo/check/consistency">>,
  <<"lib/credo/check/consistency/space_around_operators.ex">>,
  <<"lib/credo/check/consistency/space_in_parentheses">>,
  <<"lib/credo/check/consistency/space_in_parentheses/collector.ex">>,
  <<"lib/credo/check/consistency/exception_names.ex">>,
  <<"lib/credo/check/consistency/space_in_parentheses.ex">>,
  <<"lib/credo/check/consistency/multi_alias_import_require_use.ex">>,
  <<"lib/credo/check/consistency/unused_variable_names.ex">>,
  <<"lib/credo/check/consistency/parameter_pattern_matching.ex">>,
  <<"lib/credo/check/consistency/multi_alias_import_require_use">>,
  <<"lib/credo/check/consistency/multi_alias_import_require_use/collector.ex">>,
  <<"lib/credo/check/consistency/unused_variable_names">>,
  <<"lib/credo/check/consistency/unused_variable_names/collector.ex">>,
  <<"lib/credo/check/consistency/exception_names">>,
  <<"lib/credo/check/consistency/exception_names/collector.ex">>,
  <<"lib/credo/check/consistency/tabs_or_spaces">>,
  <<"lib/credo/check/consistency/tabs_or_spaces/collector.ex">>,
  <<"lib/credo/check/consistency/tabs_or_spaces.ex">>,
  <<"lib/credo/check/consistency/collector.ex">>,
  <<"lib/credo/check/consistency/space_around_operators">>,
  <<"lib/credo/check/consistency/space_around_operators/space_helper.ex">>,
  <<"lib/credo/check/consistency/space_around_operators/collector.ex">>,
  <<"lib/credo/check/consistency/parameter_pattern_matching">>,
  <<"lib/credo/check/consistency/parameter_pattern_matching/collector.ex">>,
  <<"lib/credo/check/consistency/line_endings">>,
  <<"lib/credo/check/consistency/line_endings/collector.ex">>,
  <<"lib/credo/check/consistency/line_endings.ex">>,
  <<"lib/credo/check/config_comment_finder.ex">>,
  <<"lib/credo/check/refactor">>,
  <<"lib/credo/check/refactor/function_arity.ex">>,
  <<"lib/credo/check/refactor/negated_conditions_with_else.ex">>,
  <<"lib/credo/check/refactor/filter_filter.ex">>,
  <<"lib/credo/check/refactor/nesting.ex">>,
  <<"lib/credo/check/refactor/double_boolean_negation.ex">>,
  <<"lib/credo/check/refactor/utc_now_truncate.ex">>,
  <<"lib/credo/check/refactor/long_quote_blocks.ex">>,
  <<"lib/credo/check/refactor/module_dependencies.ex">>,
  <<"lib/credo/check/refactor/reject_reject.ex">>,
  <<"lib/credo/check/refactor/append_single_item.ex">>,
  <<"lib/credo/check/refactor/filter_reject.ex">>,
  <<"lib/credo/check/refactor/io_puts.ex">>,
  <<"lib/credo/check/refactor/unless_with_else.ex">>,
  <<"lib/credo/check/refactor/map_map.ex">>,
  <<"lib/credo/check/refactor/filter_count.ex">>,
  <<"lib/credo/check/refactor/variable_rebinding.ex">>,
  <<"lib/credo/check/refactor/cyclomatic_complexity.ex">>,
  <<"lib/credo/check/refactor/negated_is_nil.ex">>,
  <<"lib/credo/check/refactor/match_in_condition.ex">>,
  <<"lib/credo/check/refactor/reject_filter.ex">>,
  <<"lib/credo/check/refactor/negated_conditions_in_unless.ex">>,
  <<"lib/credo/check/refactor/map_join.ex">>,
  <<"lib/credo/check/refactor/with_clauses.ex">>,
  <<"lib/credo/check/refactor/map_into.ex">>,
  <<"lib/credo/check/refactor/enum_helpers.ex">>,
  <<"lib/credo/check/refactor/perceived_complexity.ex">>,
  <<"lib/credo/check/refactor/abc_size.ex">>,
  <<"lib/credo/check/refactor/pass_async_in_test_cases.ex">>,
  <<"lib/credo/check/refactor/pipe_chain_start.ex">>,
  <<"lib/credo/check/refactor/redundant_with_clause_result.ex">>,
  <<"lib/credo/check/refactor/case_trivial_matches.ex">>,
  <<"lib/credo/check/refactor/apply.ex">>,
  <<"lib/credo/check/refactor/cond_statements.ex">>,<<"lib/credo/check.ex">>,
  <<"lib/credo/application.ex">>,<<"lib/credo/issue_meta.ex">>,
  <<"lib/credo/source_file.ex">>,<<"lib/credo/config_file.ex">>,
  <<"lib/credo/severity.ex">>,<<"lib/credo/watcher.ex">>,
  <<"lib/credo/config_builder.ex">>,<<"lib/credo/service">>,
  <<"lib/credo/service/source_file_scopes.ex">>,
  <<"lib/credo/service/source_file_source.ex">>,
  <<"lib/credo/service/config_files.ex">>,
  <<"lib/credo/service/source_file_scope_priorities.ex">>,
  <<"lib/credo/service/source_file_lines.ex">>,
  <<"lib/credo/service/source_file_ast.ex">>,
  <<"lib/credo/service/ets_table_helper.ex">>,<<"lib/credo/execution.ex">>,
  <<"lib/credo/code">>,<<"lib/credo/code/name.ex">>,
  <<"lib/credo/code/block.ex">>,<<"lib/credo/code/token_ast_correlation.ex">>,
  <<"lib/credo/code/strings.ex">>,<<"lib/credo/code/scope.ex">>,
  <<"lib/credo/code/parameters.ex">>,<<"lib/credo/code/sigils.ex">>,
  <<"lib/credo/code/token.ex">>,<<"lib/credo/code/interpolation_helper.ex">>,
  <<"lib/credo/code/heredocs.ex">>,<<"lib/credo/code/module.ex">>,
  <<"lib/credo/code/charlists.ex">>,<<"lib/credo/sources.ex">>,
  <<"lib/credo/execution">>,
  <<"lib/credo/execution/execution_config_files.ex">>,
  <<"lib/credo/execution/execution_issues.ex">>,
  <<"lib/credo/execution/execution_timing.ex">>,
  <<"lib/credo/execution/execution_source_files.ex">>,
  <<"lib/credo/execution/task">>,
  <<"lib/credo/execution/task/parse_options.ex">>,
  <<"lib/credo/execution/task/write_debug_report.ex">>,
  <<"lib/credo/execution/task/assign_exit_status_for_issues.ex">>,
  <<"lib/credo/execution/task/set_default_command.ex">>,
  <<"lib/credo/execution/task/require_requires.ex">>,
  <<"lib/credo/execution/task/determine_command.ex">>,
  <<"lib/credo/execution/task/initialize_plugins.ex">>,
  <<"lib/credo/execution/task/initialize_command.ex">>,
  <<"lib/credo/execution/task/append_extra_config.ex">>,
  <<"lib/credo/execution/task/run_command.ex">>,
  <<"lib/credo/execution/task/append_default_config.ex">>,
  <<"lib/credo/execution/task/use_colors.ex">>,
  <<"lib/credo/execution/task/validate_config.ex">>,
  <<"lib/credo/execution/task/validate_options.ex">>,
  <<"lib/credo/execution/task/convert_cli_options_to_config.ex">>,
  <<"lib/credo/execution/task.ex">>,<<"lib/credo/issue.ex">>,<<"LICENSE">>,
  <<"mix.exs">>,<<"README.md">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"file_system">>},
   {<<"app">>,<<"file_system">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.2 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"bunt">>},
   {<<"app">>,<<"bunt">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.2.1 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"jason">>},
   {<<"app">>,<<"jason">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.

{<<"app">>,<<"combine">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,<<"A parser combinator library for Elixir projects.">>}.
{<<"elixir">>,<<"~> 1.0">>}.
{<<"files">>,
 [<<"lib/combine.ex">>,<<"lib/combine/helpers.ex">>,
  <<"lib/combine/parser_state.ex">>,<<"lib/combine/parsers/base.ex">>,
  <<"lib/combine/parsers/binary.ex">>,<<"lib/combine/parsers/text.ex">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/bitwalker/combine">>}]}.
{<<"maintainers">>,[<<"<PERSON>">>]}.
{<<"name">>,<<"combine">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"0.10.0">>}.

{<<"links">>,
 [{<<"Function reference">>,
   <<"https://ninenines.eu/docs/en/cowboy/2.12/manual/">>},
  {<<"GitHub">>,<<"https://github.com/ninenines/cowboy">>},
  {<<"Sponsor">>,<<"https://github.com/sponsors/essen">>},
  {<<"User guide">>,<<"https://ninenines.eu/docs/en/cowboy/2.12/guide/">>}]}.
{<<"name">>,<<"cowboy">>}.
{<<"version">>,<<"2.12.0">>}.
{<<"description">>,<<"Small, fast, modern HTTP server.">>}.
{<<"app">>,<<"cowboy">>}.
{<<"build_tools">>,[<<"make">>,<<"rebar3">>]}.
{<<"files">>,
 [<<"ebin/cowboy.app">>,<<"erlang.mk">>,<<"LICENSE">>,<<"Makefile">>,
  <<"plugins.mk">>,<<"README.asciidoc">>,<<"rebar.config">>,
  <<"src/cowboy.erl">>,<<"src/cowboy_app.erl">>,<<"src/cowboy_bstr.erl">>,
  <<"src/cowboy_children.erl">>,<<"src/cowboy_clear.erl">>,
  <<"src/cowboy_clock.erl">>,<<"src/cowboy_compress_h.erl">>,
  <<"src/cowboy_constraints.erl">>,<<"src/cowboy_decompress_h.erl">>,
  <<"src/cowboy_handler.erl">>,<<"src/cowboy_http.erl">>,
  <<"src/cowboy_http2.erl">>,<<"src/cowboy_loop.erl">>,
  <<"src/cowboy_metrics_h.erl">>,<<"src/cowboy_middleware.erl">>,
  <<"src/cowboy_req.erl">>,<<"src/cowboy_rest.erl">>,
  <<"src/cowboy_router.erl">>,<<"src/cowboy_static.erl">>,
  <<"src/cowboy_stream.erl">>,<<"src/cowboy_stream_h.erl">>,
  <<"src/cowboy_sub_protocol.erl">>,<<"src/cowboy_sup.erl">>,
  <<"src/cowboy_tls.erl">>,<<"src/cowboy_tracer_h.erl">>,
  <<"src/cowboy_websocket.erl">>]}.
{<<"requirements">>,
 [{<<"cowlib">>,
   [{<<"app">>,<<"cowlib">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"2.13.0">>}]},
  {<<"ranch">>,
   [{<<"app">>,<<"ranch">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"1.8.0">>}]}]}.
{<<"licenses">>,[<<"ISC">>]}.

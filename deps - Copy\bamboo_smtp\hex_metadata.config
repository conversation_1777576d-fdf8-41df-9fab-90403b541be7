{<<"app">>,<<"bamboo_smtp">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,<<"A Bamboo adapter for SMTP">>}.
{<<"elixir">>,<<"~> 1.4">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/bamboo">>,<<"lib/bamboo/adapters">>,
  <<"lib/bamboo/adapters/smtp_adapter.ex">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE.md">>,<<"CHANGELOG.md">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/fewlinesco/bamboo_smtp">>}]}.
{<<"name">>,<<"bamboo_smtp">>}.
{<<"requirements">>,
 [[{<<"app">>,<<"bamboo">>},
   {<<"name">>,<<"bamboo">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.2">>}],
  [{<<"app">>,<<"gen_smtp">>},
   {<<"name">>,<<"gen_smtp">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 0.15.0">>}]]}.
{<<"version">>,<<"2.1.0">>}.

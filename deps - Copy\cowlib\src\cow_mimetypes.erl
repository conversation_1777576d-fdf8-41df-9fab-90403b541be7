%% Copyright (c) 2013-2023, <PERSON><PERSON><PERSON> <<EMAIL>>
%%
%% Permission to use, copy, modify, and/or distribute this software for any
%% purpose with or without fee is hereby granted, provided that the above
%% copyright notice and this permission notice appear in all copies.
%%
%% THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
%% WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
%% MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
%% ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
%% WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
%% ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
%% OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

-module(cow_mimetypes).

-export([all/1]).
-export([web/1]).

%% @doc Return the mimetype for any file by looking at its extension.

-spec all(binary()) -> {binary(), binary(), []}.
all(Path) ->
	case filename:extension(Path) of
		<<>> -> {<<"application">>, <<"octet-stream">>, []};
		%% @todo Convert to string:lowercase on OTP-20+.
		<< $., Ext/binary >> -> all_ext(list_to_binary(string:to_lower(binary_to_list(Ext))))
	end.

%% @doc Return the mimetype for a Web related file by looking at its extension.

-spec web(binary()) -> {binary(), binary(), []}.
web(Path) ->
	case filename:extension(Path) of
		<<>> -> {<<"application">>, <<"octet-stream">>, []};
		%% @todo Convert to string:lowercase on OTP-20+.
		<< $., Ext/binary >> -> web_ext(list_to_binary(string:to_lower(binary_to_list(Ext))))
	end.

%% Internal.

%% GENERATED
all_ext(<<"123">>) -> {<<"application">>, <<"vnd.lotus-1-2-3">>, []};
all_ext(<<"3dml">>) -> {<<"text">>, <<"vnd.in3d.3dml">>, []};
all_ext(<<"3ds">>) -> {<<"image">>, <<"x-3ds">>, []};
all_ext(<<"3g2">>) -> {<<"video">>, <<"3gpp2">>, []};
all_ext(<<"3gp">>) -> {<<"video">>, <<"3gpp">>, []};
all_ext(<<"7z">>) -> {<<"application">>, <<"x-7z-compressed">>, []};
all_ext(<<"aab">>) -> {<<"application">>, <<"x-authorware-bin">>, []};
all_ext(<<"aac">>) -> {<<"audio">>, <<"x-aac">>, []};
all_ext(<<"aam">>) -> {<<"application">>, <<"x-authorware-map">>, []};
all_ext(<<"aas">>) -> {<<"application">>, <<"x-authorware-seg">>, []};
all_ext(<<"abw">>) -> {<<"application">>, <<"x-abiword">>, []};
all_ext(<<"ac">>) -> {<<"application">>, <<"pkix-attr-cert">>, []};
all_ext(<<"acc">>) -> {<<"application">>, <<"vnd.americandynamics.acc">>, []};
all_ext(<<"ace">>) -> {<<"application">>, <<"x-ace-compressed">>, []};
all_ext(<<"acu">>) -> {<<"application">>, <<"vnd.acucobol">>, []};
all_ext(<<"acutc">>) -> {<<"application">>, <<"vnd.acucorp">>, []};
all_ext(<<"adp">>) -> {<<"audio">>, <<"adpcm">>, []};
all_ext(<<"aep">>) -> {<<"application">>, <<"vnd.audiograph">>, []};
all_ext(<<"afm">>) -> {<<"application">>, <<"x-font-type1">>, []};
all_ext(<<"afp">>) -> {<<"application">>, <<"vnd.ibm.modcap">>, []};
all_ext(<<"ahead">>) -> {<<"application">>, <<"vnd.ahead.space">>, []};
all_ext(<<"ai">>) -> {<<"application">>, <<"postscript">>, []};
all_ext(<<"aif">>) -> {<<"audio">>, <<"x-aiff">>, []};
all_ext(<<"aifc">>) -> {<<"audio">>, <<"x-aiff">>, []};
all_ext(<<"aiff">>) -> {<<"audio">>, <<"x-aiff">>, []};
all_ext(<<"air">>) -> {<<"application">>, <<"vnd.adobe.air-application-installer-package+zip">>, []};
all_ext(<<"ait">>) -> {<<"application">>, <<"vnd.dvb.ait">>, []};
all_ext(<<"ami">>) -> {<<"application">>, <<"vnd.amiga.ami">>, []};
all_ext(<<"apk">>) -> {<<"application">>, <<"vnd.android.package-archive">>, []};
all_ext(<<"appcache">>) -> {<<"text">>, <<"cache-manifest">>, []};
all_ext(<<"application">>) -> {<<"application">>, <<"x-ms-application">>, []};
all_ext(<<"apr">>) -> {<<"application">>, <<"vnd.lotus-approach">>, []};
all_ext(<<"arc">>) -> {<<"application">>, <<"x-freearc">>, []};
all_ext(<<"asc">>) -> {<<"application">>, <<"pgp-signature">>, []};
all_ext(<<"asf">>) -> {<<"video">>, <<"x-ms-asf">>, []};
all_ext(<<"asm">>) -> {<<"text">>, <<"x-asm">>, []};
all_ext(<<"aso">>) -> {<<"application">>, <<"vnd.accpac.simply.aso">>, []};
all_ext(<<"asx">>) -> {<<"video">>, <<"x-ms-asf">>, []};
all_ext(<<"atc">>) -> {<<"application">>, <<"vnd.acucorp">>, []};
all_ext(<<"atom">>) -> {<<"application">>, <<"atom+xml">>, []};
all_ext(<<"atomcat">>) -> {<<"application">>, <<"atomcat+xml">>, []};
all_ext(<<"atomsvc">>) -> {<<"application">>, <<"atomsvc+xml">>, []};
all_ext(<<"atx">>) -> {<<"application">>, <<"vnd.antix.game-component">>, []};
all_ext(<<"au">>) -> {<<"audio">>, <<"basic">>, []};
all_ext(<<"avi">>) -> {<<"video">>, <<"x-msvideo">>, []};
all_ext(<<"aw">>) -> {<<"application">>, <<"applixware">>, []};
all_ext(<<"azf">>) -> {<<"application">>, <<"vnd.airzip.filesecure.azf">>, []};
all_ext(<<"azs">>) -> {<<"application">>, <<"vnd.airzip.filesecure.azs">>, []};
all_ext(<<"azw">>) -> {<<"application">>, <<"vnd.amazon.ebook">>, []};
all_ext(<<"bat">>) -> {<<"application">>, <<"x-msdownload">>, []};
all_ext(<<"bcpio">>) -> {<<"application">>, <<"x-bcpio">>, []};
all_ext(<<"bdf">>) -> {<<"application">>, <<"x-font-bdf">>, []};
all_ext(<<"bdm">>) -> {<<"application">>, <<"vnd.syncml.dm+wbxml">>, []};
all_ext(<<"bed">>) -> {<<"application">>, <<"vnd.realvnc.bed">>, []};
all_ext(<<"bh2">>) -> {<<"application">>, <<"vnd.fujitsu.oasysprs">>, []};
all_ext(<<"bin">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"blb">>) -> {<<"application">>, <<"x-blorb">>, []};
all_ext(<<"blorb">>) -> {<<"application">>, <<"x-blorb">>, []};
all_ext(<<"bmi">>) -> {<<"application">>, <<"vnd.bmi">>, []};
all_ext(<<"bmp">>) -> {<<"image">>, <<"bmp">>, []};
all_ext(<<"book">>) -> {<<"application">>, <<"vnd.framemaker">>, []};
all_ext(<<"box">>) -> {<<"application">>, <<"vnd.previewsystems.box">>, []};
all_ext(<<"boz">>) -> {<<"application">>, <<"x-bzip2">>, []};
all_ext(<<"bpk">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"btif">>) -> {<<"image">>, <<"prs.btif">>, []};
all_ext(<<"bz2">>) -> {<<"application">>, <<"x-bzip2">>, []};
all_ext(<<"bz">>) -> {<<"application">>, <<"x-bzip">>, []};
all_ext(<<"c11amc">>) -> {<<"application">>, <<"vnd.cluetrust.cartomobile-config">>, []};
all_ext(<<"c11amz">>) -> {<<"application">>, <<"vnd.cluetrust.cartomobile-config-pkg">>, []};
all_ext(<<"c4d">>) -> {<<"application">>, <<"vnd.clonk.c4group">>, []};
all_ext(<<"c4f">>) -> {<<"application">>, <<"vnd.clonk.c4group">>, []};
all_ext(<<"c4g">>) -> {<<"application">>, <<"vnd.clonk.c4group">>, []};
all_ext(<<"c4p">>) -> {<<"application">>, <<"vnd.clonk.c4group">>, []};
all_ext(<<"c4u">>) -> {<<"application">>, <<"vnd.clonk.c4group">>, []};
all_ext(<<"cab">>) -> {<<"application">>, <<"vnd.ms-cab-compressed">>, []};
all_ext(<<"caf">>) -> {<<"audio">>, <<"x-caf">>, []};
all_ext(<<"cap">>) -> {<<"application">>, <<"vnd.tcpdump.pcap">>, []};
all_ext(<<"car">>) -> {<<"application">>, <<"vnd.curl.car">>, []};
all_ext(<<"cat">>) -> {<<"application">>, <<"vnd.ms-pki.seccat">>, []};
all_ext(<<"cb7">>) -> {<<"application">>, <<"x-cbr">>, []};
all_ext(<<"cba">>) -> {<<"application">>, <<"x-cbr">>, []};
all_ext(<<"cbr">>) -> {<<"application">>, <<"x-cbr">>, []};
all_ext(<<"cbt">>) -> {<<"application">>, <<"x-cbr">>, []};
all_ext(<<"cbz">>) -> {<<"application">>, <<"x-cbr">>, []};
all_ext(<<"cct">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"cc">>) -> {<<"text">>, <<"x-c">>, []};
all_ext(<<"ccxml">>) -> {<<"application">>, <<"ccxml+xml">>, []};
all_ext(<<"cdbcmsg">>) -> {<<"application">>, <<"vnd.contact.cmsg">>, []};
all_ext(<<"cdf">>) -> {<<"application">>, <<"x-netcdf">>, []};
all_ext(<<"cdkey">>) -> {<<"application">>, <<"vnd.mediastation.cdkey">>, []};
all_ext(<<"cdmia">>) -> {<<"application">>, <<"cdmi-capability">>, []};
all_ext(<<"cdmic">>) -> {<<"application">>, <<"cdmi-container">>, []};
all_ext(<<"cdmid">>) -> {<<"application">>, <<"cdmi-domain">>, []};
all_ext(<<"cdmio">>) -> {<<"application">>, <<"cdmi-object">>, []};
all_ext(<<"cdmiq">>) -> {<<"application">>, <<"cdmi-queue">>, []};
all_ext(<<"cdx">>) -> {<<"chemical">>, <<"x-cdx">>, []};
all_ext(<<"cdxml">>) -> {<<"application">>, <<"vnd.chemdraw+xml">>, []};
all_ext(<<"cdy">>) -> {<<"application">>, <<"vnd.cinderella">>, []};
all_ext(<<"cer">>) -> {<<"application">>, <<"pkix-cert">>, []};
all_ext(<<"cfs">>) -> {<<"application">>, <<"x-cfs-compressed">>, []};
all_ext(<<"cgm">>) -> {<<"image">>, <<"cgm">>, []};
all_ext(<<"chat">>) -> {<<"application">>, <<"x-chat">>, []};
all_ext(<<"chm">>) -> {<<"application">>, <<"vnd.ms-htmlhelp">>, []};
all_ext(<<"chrt">>) -> {<<"application">>, <<"vnd.kde.kchart">>, []};
all_ext(<<"cif">>) -> {<<"chemical">>, <<"x-cif">>, []};
all_ext(<<"cii">>) -> {<<"application">>, <<"vnd.anser-web-certificate-issue-initiation">>, []};
all_ext(<<"cil">>) -> {<<"application">>, <<"vnd.ms-artgalry">>, []};
all_ext(<<"cla">>) -> {<<"application">>, <<"vnd.claymore">>, []};
all_ext(<<"class">>) -> {<<"application">>, <<"java-vm">>, []};
all_ext(<<"clkk">>) -> {<<"application">>, <<"vnd.crick.clicker.keyboard">>, []};
all_ext(<<"clkp">>) -> {<<"application">>, <<"vnd.crick.clicker.palette">>, []};
all_ext(<<"clkt">>) -> {<<"application">>, <<"vnd.crick.clicker.template">>, []};
all_ext(<<"clkw">>) -> {<<"application">>, <<"vnd.crick.clicker.wordbank">>, []};
all_ext(<<"clkx">>) -> {<<"application">>, <<"vnd.crick.clicker">>, []};
all_ext(<<"clp">>) -> {<<"application">>, <<"x-msclip">>, []};
all_ext(<<"cmc">>) -> {<<"application">>, <<"vnd.cosmocaller">>, []};
all_ext(<<"cmdf">>) -> {<<"chemical">>, <<"x-cmdf">>, []};
all_ext(<<"cml">>) -> {<<"chemical">>, <<"x-cml">>, []};
all_ext(<<"cmp">>) -> {<<"application">>, <<"vnd.yellowriver-custom-menu">>, []};
all_ext(<<"cmx">>) -> {<<"image">>, <<"x-cmx">>, []};
all_ext(<<"cod">>) -> {<<"application">>, <<"vnd.rim.cod">>, []};
all_ext(<<"com">>) -> {<<"application">>, <<"x-msdownload">>, []};
all_ext(<<"conf">>) -> {<<"text">>, <<"plain">>, []};
all_ext(<<"cpio">>) -> {<<"application">>, <<"x-cpio">>, []};
all_ext(<<"cpp">>) -> {<<"text">>, <<"x-c">>, []};
all_ext(<<"cpt">>) -> {<<"application">>, <<"mac-compactpro">>, []};
all_ext(<<"crd">>) -> {<<"application">>, <<"x-mscardfile">>, []};
all_ext(<<"crl">>) -> {<<"application">>, <<"pkix-crl">>, []};
all_ext(<<"crt">>) -> {<<"application">>, <<"x-x509-ca-cert">>, []};
all_ext(<<"cryptonote">>) -> {<<"application">>, <<"vnd.rig.cryptonote">>, []};
all_ext(<<"csh">>) -> {<<"application">>, <<"x-csh">>, []};
all_ext(<<"csml">>) -> {<<"chemical">>, <<"x-csml">>, []};
all_ext(<<"csp">>) -> {<<"application">>, <<"vnd.commonspace">>, []};
all_ext(<<"css">>) -> {<<"text">>, <<"css">>, []};
all_ext(<<"cst">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"csv">>) -> {<<"text">>, <<"csv">>, []};
all_ext(<<"c">>) -> {<<"text">>, <<"x-c">>, []};
all_ext(<<"cu">>) -> {<<"application">>, <<"cu-seeme">>, []};
all_ext(<<"curl">>) -> {<<"text">>, <<"vnd.curl">>, []};
all_ext(<<"cww">>) -> {<<"application">>, <<"prs.cww">>, []};
all_ext(<<"cxt">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"cxx">>) -> {<<"text">>, <<"x-c">>, []};
all_ext(<<"dae">>) -> {<<"model">>, <<"vnd.collada+xml">>, []};
all_ext(<<"daf">>) -> {<<"application">>, <<"vnd.mobius.daf">>, []};
all_ext(<<"dart">>) -> {<<"application">>, <<"vnd.dart">>, []};
all_ext(<<"dataless">>) -> {<<"application">>, <<"vnd.fdsn.seed">>, []};
all_ext(<<"davmount">>) -> {<<"application">>, <<"davmount+xml">>, []};
all_ext(<<"dbk">>) -> {<<"application">>, <<"docbook+xml">>, []};
all_ext(<<"dcr">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"dcurl">>) -> {<<"text">>, <<"vnd.curl.dcurl">>, []};
all_ext(<<"dd2">>) -> {<<"application">>, <<"vnd.oma.dd2+xml">>, []};
all_ext(<<"ddd">>) -> {<<"application">>, <<"vnd.fujixerox.ddd">>, []};
all_ext(<<"deb">>) -> {<<"application">>, <<"x-debian-package">>, []};
all_ext(<<"def">>) -> {<<"text">>, <<"plain">>, []};
all_ext(<<"deploy">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"der">>) -> {<<"application">>, <<"x-x509-ca-cert">>, []};
all_ext(<<"dfac">>) -> {<<"application">>, <<"vnd.dreamfactory">>, []};
all_ext(<<"dgc">>) -> {<<"application">>, <<"x-dgc-compressed">>, []};
all_ext(<<"dic">>) -> {<<"text">>, <<"x-c">>, []};
all_ext(<<"dir">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"dis">>) -> {<<"application">>, <<"vnd.mobius.dis">>, []};
all_ext(<<"dist">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"distz">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"djv">>) -> {<<"image">>, <<"vnd.djvu">>, []};
all_ext(<<"djvu">>) -> {<<"image">>, <<"vnd.djvu">>, []};
all_ext(<<"dll">>) -> {<<"application">>, <<"x-msdownload">>, []};
all_ext(<<"dmg">>) -> {<<"application">>, <<"x-apple-diskimage">>, []};
all_ext(<<"dmp">>) -> {<<"application">>, <<"vnd.tcpdump.pcap">>, []};
all_ext(<<"dms">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"dna">>) -> {<<"application">>, <<"vnd.dna">>, []};
all_ext(<<"doc">>) -> {<<"application">>, <<"msword">>, []};
all_ext(<<"docm">>) -> {<<"application">>, <<"vnd.ms-word.document.macroenabled.12">>, []};
all_ext(<<"docx">>) -> {<<"application">>, <<"vnd.openxmlformats-officedocument.wordprocessingml.document">>, []};
all_ext(<<"dot">>) -> {<<"application">>, <<"msword">>, []};
all_ext(<<"dotm">>) -> {<<"application">>, <<"vnd.ms-word.template.macroenabled.12">>, []};
all_ext(<<"dotx">>) -> {<<"application">>, <<"vnd.openxmlformats-officedocument.wordprocessingml.template">>, []};
all_ext(<<"dp">>) -> {<<"application">>, <<"vnd.osgi.dp">>, []};
all_ext(<<"dpg">>) -> {<<"application">>, <<"vnd.dpgraph">>, []};
all_ext(<<"dra">>) -> {<<"audio">>, <<"vnd.dra">>, []};
all_ext(<<"dsc">>) -> {<<"text">>, <<"prs.lines.tag">>, []};
all_ext(<<"dssc">>) -> {<<"application">>, <<"dssc+der">>, []};
all_ext(<<"dtb">>) -> {<<"application">>, <<"x-dtbook+xml">>, []};
all_ext(<<"dtd">>) -> {<<"application">>, <<"xml-dtd">>, []};
all_ext(<<"dts">>) -> {<<"audio">>, <<"vnd.dts">>, []};
all_ext(<<"dtshd">>) -> {<<"audio">>, <<"vnd.dts.hd">>, []};
all_ext(<<"dump">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"dvb">>) -> {<<"video">>, <<"vnd.dvb.file">>, []};
all_ext(<<"dvi">>) -> {<<"application">>, <<"x-dvi">>, []};
all_ext(<<"dwf">>) -> {<<"model">>, <<"vnd.dwf">>, []};
all_ext(<<"dwg">>) -> {<<"image">>, <<"vnd.dwg">>, []};
all_ext(<<"dxf">>) -> {<<"image">>, <<"vnd.dxf">>, []};
all_ext(<<"dxp">>) -> {<<"application">>, <<"vnd.spotfire.dxp">>, []};
all_ext(<<"dxr">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"ecelp4800">>) -> {<<"audio">>, <<"vnd.nuera.ecelp4800">>, []};
all_ext(<<"ecelp7470">>) -> {<<"audio">>, <<"vnd.nuera.ecelp7470">>, []};
all_ext(<<"ecelp9600">>) -> {<<"audio">>, <<"vnd.nuera.ecelp9600">>, []};
all_ext(<<"ecma">>) -> {<<"application">>, <<"ecmascript">>, []};
all_ext(<<"edm">>) -> {<<"application">>, <<"vnd.novadigm.edm">>, []};
all_ext(<<"edx">>) -> {<<"application">>, <<"vnd.novadigm.edx">>, []};
all_ext(<<"efif">>) -> {<<"application">>, <<"vnd.picsel">>, []};
all_ext(<<"ei6">>) -> {<<"application">>, <<"vnd.pg.osasli">>, []};
all_ext(<<"elc">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"emf">>) -> {<<"application">>, <<"x-msmetafile">>, []};
all_ext(<<"eml">>) -> {<<"message">>, <<"rfc822">>, []};
all_ext(<<"emma">>) -> {<<"application">>, <<"emma+xml">>, []};
all_ext(<<"emz">>) -> {<<"application">>, <<"x-msmetafile">>, []};
all_ext(<<"eol">>) -> {<<"audio">>, <<"vnd.digital-winds">>, []};
all_ext(<<"eot">>) -> {<<"application">>, <<"vnd.ms-fontobject">>, []};
all_ext(<<"eps">>) -> {<<"application">>, <<"postscript">>, []};
all_ext(<<"epub">>) -> {<<"application">>, <<"epub+zip">>, []};
all_ext(<<"es3">>) -> {<<"application">>, <<"vnd.eszigno3+xml">>, []};
all_ext(<<"esa">>) -> {<<"application">>, <<"vnd.osgi.subsystem">>, []};
all_ext(<<"esf">>) -> {<<"application">>, <<"vnd.epson.esf">>, []};
all_ext(<<"et3">>) -> {<<"application">>, <<"vnd.eszigno3+xml">>, []};
all_ext(<<"etx">>) -> {<<"text">>, <<"x-setext">>, []};
all_ext(<<"eva">>) -> {<<"application">>, <<"x-eva">>, []};
all_ext(<<"evy">>) -> {<<"application">>, <<"x-envoy">>, []};
all_ext(<<"exe">>) -> {<<"application">>, <<"x-msdownload">>, []};
all_ext(<<"exi">>) -> {<<"application">>, <<"exi">>, []};
all_ext(<<"ext">>) -> {<<"application">>, <<"vnd.novadigm.ext">>, []};
all_ext(<<"ez2">>) -> {<<"application">>, <<"vnd.ezpix-album">>, []};
all_ext(<<"ez3">>) -> {<<"application">>, <<"vnd.ezpix-package">>, []};
all_ext(<<"ez">>) -> {<<"application">>, <<"andrew-inset">>, []};
all_ext(<<"f4v">>) -> {<<"video">>, <<"x-f4v">>, []};
all_ext(<<"f77">>) -> {<<"text">>, <<"x-fortran">>, []};
all_ext(<<"f90">>) -> {<<"text">>, <<"x-fortran">>, []};
all_ext(<<"fbs">>) -> {<<"image">>, <<"vnd.fastbidsheet">>, []};
all_ext(<<"fcdt">>) -> {<<"application">>, <<"vnd.adobe.formscentral.fcdt">>, []};
all_ext(<<"fcs">>) -> {<<"application">>, <<"vnd.isac.fcs">>, []};
all_ext(<<"fdf">>) -> {<<"application">>, <<"vnd.fdf">>, []};
all_ext(<<"fe_launch">>) -> {<<"application">>, <<"vnd.denovo.fcselayout-link">>, []};
all_ext(<<"fg5">>) -> {<<"application">>, <<"vnd.fujitsu.oasysgp">>, []};
all_ext(<<"fgd">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"fh4">>) -> {<<"image">>, <<"x-freehand">>, []};
all_ext(<<"fh5">>) -> {<<"image">>, <<"x-freehand">>, []};
all_ext(<<"fh7">>) -> {<<"image">>, <<"x-freehand">>, []};
all_ext(<<"fhc">>) -> {<<"image">>, <<"x-freehand">>, []};
all_ext(<<"fh">>) -> {<<"image">>, <<"x-freehand">>, []};
all_ext(<<"fig">>) -> {<<"application">>, <<"x-xfig">>, []};
all_ext(<<"flac">>) -> {<<"audio">>, <<"x-flac">>, []};
all_ext(<<"fli">>) -> {<<"video">>, <<"x-fli">>, []};
all_ext(<<"flo">>) -> {<<"application">>, <<"vnd.micrografx.flo">>, []};
all_ext(<<"flv">>) -> {<<"video">>, <<"x-flv">>, []};
all_ext(<<"flw">>) -> {<<"application">>, <<"vnd.kde.kivio">>, []};
all_ext(<<"flx">>) -> {<<"text">>, <<"vnd.fmi.flexstor">>, []};
all_ext(<<"fly">>) -> {<<"text">>, <<"vnd.fly">>, []};
all_ext(<<"fm">>) -> {<<"application">>, <<"vnd.framemaker">>, []};
all_ext(<<"fnc">>) -> {<<"application">>, <<"vnd.frogans.fnc">>, []};
all_ext(<<"for">>) -> {<<"text">>, <<"x-fortran">>, []};
all_ext(<<"fpx">>) -> {<<"image">>, <<"vnd.fpx">>, []};
all_ext(<<"frame">>) -> {<<"application">>, <<"vnd.framemaker">>, []};
all_ext(<<"fsc">>) -> {<<"application">>, <<"vnd.fsc.weblaunch">>, []};
all_ext(<<"fst">>) -> {<<"image">>, <<"vnd.fst">>, []};
all_ext(<<"ftc">>) -> {<<"application">>, <<"vnd.fluxtime.clip">>, []};
all_ext(<<"f">>) -> {<<"text">>, <<"x-fortran">>, []};
all_ext(<<"fti">>) -> {<<"application">>, <<"vnd.anser-web-funds-transfer-initiation">>, []};
all_ext(<<"fvt">>) -> {<<"video">>, <<"vnd.fvt">>, []};
all_ext(<<"fxp">>) -> {<<"application">>, <<"vnd.adobe.fxp">>, []};
all_ext(<<"fxpl">>) -> {<<"application">>, <<"vnd.adobe.fxp">>, []};
all_ext(<<"fzs">>) -> {<<"application">>, <<"vnd.fuzzysheet">>, []};
all_ext(<<"g2w">>) -> {<<"application">>, <<"vnd.geoplan">>, []};
all_ext(<<"g3">>) -> {<<"image">>, <<"g3fax">>, []};
all_ext(<<"g3w">>) -> {<<"application">>, <<"vnd.geospace">>, []};
all_ext(<<"gac">>) -> {<<"application">>, <<"vnd.groove-account">>, []};
all_ext(<<"gam">>) -> {<<"application">>, <<"x-tads">>, []};
all_ext(<<"gbr">>) -> {<<"application">>, <<"rpki-ghostbusters">>, []};
all_ext(<<"gca">>) -> {<<"application">>, <<"x-gca-compressed">>, []};
all_ext(<<"gdl">>) -> {<<"model">>, <<"vnd.gdl">>, []};
all_ext(<<"geo">>) -> {<<"application">>, <<"vnd.dynageo">>, []};
all_ext(<<"gex">>) -> {<<"application">>, <<"vnd.geometry-explorer">>, []};
all_ext(<<"ggb">>) -> {<<"application">>, <<"vnd.geogebra.file">>, []};
all_ext(<<"ggt">>) -> {<<"application">>, <<"vnd.geogebra.tool">>, []};
all_ext(<<"ghf">>) -> {<<"application">>, <<"vnd.groove-help">>, []};
all_ext(<<"gif">>) -> {<<"image">>, <<"gif">>, []};
all_ext(<<"gim">>) -> {<<"application">>, <<"vnd.groove-identity-message">>, []};
all_ext(<<"gml">>) -> {<<"application">>, <<"gml+xml">>, []};
all_ext(<<"gmx">>) -> {<<"application">>, <<"vnd.gmx">>, []};
all_ext(<<"gnumeric">>) -> {<<"application">>, <<"x-gnumeric">>, []};
all_ext(<<"gph">>) -> {<<"application">>, <<"vnd.flographit">>, []};
all_ext(<<"gpx">>) -> {<<"application">>, <<"gpx+xml">>, []};
all_ext(<<"gqf">>) -> {<<"application">>, <<"vnd.grafeq">>, []};
all_ext(<<"gqs">>) -> {<<"application">>, <<"vnd.grafeq">>, []};
all_ext(<<"gram">>) -> {<<"application">>, <<"srgs">>, []};
all_ext(<<"gramps">>) -> {<<"application">>, <<"x-gramps-xml">>, []};
all_ext(<<"gre">>) -> {<<"application">>, <<"vnd.geometry-explorer">>, []};
all_ext(<<"grv">>) -> {<<"application">>, <<"vnd.groove-injector">>, []};
all_ext(<<"grxml">>) -> {<<"application">>, <<"srgs+xml">>, []};
all_ext(<<"gsf">>) -> {<<"application">>, <<"x-font-ghostscript">>, []};
all_ext(<<"gtar">>) -> {<<"application">>, <<"x-gtar">>, []};
all_ext(<<"gtm">>) -> {<<"application">>, <<"vnd.groove-tool-message">>, []};
all_ext(<<"gtw">>) -> {<<"model">>, <<"vnd.gtw">>, []};
all_ext(<<"gv">>) -> {<<"text">>, <<"vnd.graphviz">>, []};
all_ext(<<"gxf">>) -> {<<"application">>, <<"gxf">>, []};
all_ext(<<"gxt">>) -> {<<"application">>, <<"vnd.geonext">>, []};
all_ext(<<"h261">>) -> {<<"video">>, <<"h261">>, []};
all_ext(<<"h263">>) -> {<<"video">>, <<"h263">>, []};
all_ext(<<"h264">>) -> {<<"video">>, <<"h264">>, []};
all_ext(<<"hal">>) -> {<<"application">>, <<"vnd.hal+xml">>, []};
all_ext(<<"hbci">>) -> {<<"application">>, <<"vnd.hbci">>, []};
all_ext(<<"hdf">>) -> {<<"application">>, <<"x-hdf">>, []};
all_ext(<<"hh">>) -> {<<"text">>, <<"x-c">>, []};
all_ext(<<"hlp">>) -> {<<"application">>, <<"winhlp">>, []};
all_ext(<<"hpgl">>) -> {<<"application">>, <<"vnd.hp-hpgl">>, []};
all_ext(<<"hpid">>) -> {<<"application">>, <<"vnd.hp-hpid">>, []};
all_ext(<<"hps">>) -> {<<"application">>, <<"vnd.hp-hps">>, []};
all_ext(<<"hqx">>) -> {<<"application">>, <<"mac-binhex40">>, []};
all_ext(<<"h">>) -> {<<"text">>, <<"x-c">>, []};
all_ext(<<"htke">>) -> {<<"application">>, <<"vnd.kenameaapp">>, []};
all_ext(<<"html">>) -> {<<"text">>, <<"html">>, []};
all_ext(<<"htm">>) -> {<<"text">>, <<"html">>, []};
all_ext(<<"hvd">>) -> {<<"application">>, <<"vnd.yamaha.hv-dic">>, []};
all_ext(<<"hvp">>) -> {<<"application">>, <<"vnd.yamaha.hv-voice">>, []};
all_ext(<<"hvs">>) -> {<<"application">>, <<"vnd.yamaha.hv-script">>, []};
all_ext(<<"i2g">>) -> {<<"application">>, <<"vnd.intergeo">>, []};
all_ext(<<"icc">>) -> {<<"application">>, <<"vnd.iccprofile">>, []};
all_ext(<<"ice">>) -> {<<"x-conference">>, <<"x-cooltalk">>, []};
all_ext(<<"icm">>) -> {<<"application">>, <<"vnd.iccprofile">>, []};
all_ext(<<"ico">>) -> {<<"image">>, <<"x-icon">>, []};
all_ext(<<"ics">>) -> {<<"text">>, <<"calendar">>, []};
all_ext(<<"ief">>) -> {<<"image">>, <<"ief">>, []};
all_ext(<<"ifb">>) -> {<<"text">>, <<"calendar">>, []};
all_ext(<<"ifm">>) -> {<<"application">>, <<"vnd.shana.informed.formdata">>, []};
all_ext(<<"iges">>) -> {<<"model">>, <<"iges">>, []};
all_ext(<<"igl">>) -> {<<"application">>, <<"vnd.igloader">>, []};
all_ext(<<"igm">>) -> {<<"application">>, <<"vnd.insors.igm">>, []};
all_ext(<<"igs">>) -> {<<"model">>, <<"iges">>, []};
all_ext(<<"igx">>) -> {<<"application">>, <<"vnd.micrografx.igx">>, []};
all_ext(<<"iif">>) -> {<<"application">>, <<"vnd.shana.informed.interchange">>, []};
all_ext(<<"imp">>) -> {<<"application">>, <<"vnd.accpac.simply.imp">>, []};
all_ext(<<"ims">>) -> {<<"application">>, <<"vnd.ms-ims">>, []};
all_ext(<<"ink">>) -> {<<"application">>, <<"inkml+xml">>, []};
all_ext(<<"inkml">>) -> {<<"application">>, <<"inkml+xml">>, []};
all_ext(<<"install">>) -> {<<"application">>, <<"x-install-instructions">>, []};
all_ext(<<"in">>) -> {<<"text">>, <<"plain">>, []};
all_ext(<<"iota">>) -> {<<"application">>, <<"vnd.astraea-software.iota">>, []};
all_ext(<<"ipfix">>) -> {<<"application">>, <<"ipfix">>, []};
all_ext(<<"ipk">>) -> {<<"application">>, <<"vnd.shana.informed.package">>, []};
all_ext(<<"irm">>) -> {<<"application">>, <<"vnd.ibm.rights-management">>, []};
all_ext(<<"irp">>) -> {<<"application">>, <<"vnd.irepository.package+xml">>, []};
all_ext(<<"iso">>) -> {<<"application">>, <<"x-iso9660-image">>, []};
all_ext(<<"itp">>) -> {<<"application">>, <<"vnd.shana.informed.formtemplate">>, []};
all_ext(<<"ivp">>) -> {<<"application">>, <<"vnd.immervision-ivp">>, []};
all_ext(<<"ivu">>) -> {<<"application">>, <<"vnd.immervision-ivu">>, []};
all_ext(<<"jad">>) -> {<<"text">>, <<"vnd.sun.j2me.app-descriptor">>, []};
all_ext(<<"jam">>) -> {<<"application">>, <<"vnd.jam">>, []};
all_ext(<<"jar">>) -> {<<"application">>, <<"java-archive">>, []};
all_ext(<<"java">>) -> {<<"text">>, <<"x-java-source">>, []};
all_ext(<<"jisp">>) -> {<<"application">>, <<"vnd.jisp">>, []};
all_ext(<<"jlt">>) -> {<<"application">>, <<"vnd.hp-jlyt">>, []};
all_ext(<<"jnlp">>) -> {<<"application">>, <<"x-java-jnlp-file">>, []};
all_ext(<<"joda">>) -> {<<"application">>, <<"vnd.joost.joda-archive">>, []};
all_ext(<<"jpeg">>) -> {<<"image">>, <<"jpeg">>, []};
all_ext(<<"jpe">>) -> {<<"image">>, <<"jpeg">>, []};
all_ext(<<"jpg">>) -> {<<"image">>, <<"jpeg">>, []};
all_ext(<<"jpgm">>) -> {<<"video">>, <<"jpm">>, []};
all_ext(<<"jpgv">>) -> {<<"video">>, <<"jpeg">>, []};
all_ext(<<"jpm">>) -> {<<"video">>, <<"jpm">>, []};
all_ext(<<"js">>) -> {<<"application">>, <<"javascript">>, []};
all_ext(<<"json">>) -> {<<"application">>, <<"json">>, []};
all_ext(<<"jsonml">>) -> {<<"application">>, <<"jsonml+json">>, []};
all_ext(<<"kar">>) -> {<<"audio">>, <<"midi">>, []};
all_ext(<<"karbon">>) -> {<<"application">>, <<"vnd.kde.karbon">>, []};
all_ext(<<"kfo">>) -> {<<"application">>, <<"vnd.kde.kformula">>, []};
all_ext(<<"kia">>) -> {<<"application">>, <<"vnd.kidspiration">>, []};
all_ext(<<"kml">>) -> {<<"application">>, <<"vnd.google-earth.kml+xml">>, []};
all_ext(<<"kmz">>) -> {<<"application">>, <<"vnd.google-earth.kmz">>, []};
all_ext(<<"kne">>) -> {<<"application">>, <<"vnd.kinar">>, []};
all_ext(<<"knp">>) -> {<<"application">>, <<"vnd.kinar">>, []};
all_ext(<<"kon">>) -> {<<"application">>, <<"vnd.kde.kontour">>, []};
all_ext(<<"kpr">>) -> {<<"application">>, <<"vnd.kde.kpresenter">>, []};
all_ext(<<"kpt">>) -> {<<"application">>, <<"vnd.kde.kpresenter">>, []};
all_ext(<<"kpxx">>) -> {<<"application">>, <<"vnd.ds-keypoint">>, []};
all_ext(<<"ksp">>) -> {<<"application">>, <<"vnd.kde.kspread">>, []};
all_ext(<<"ktr">>) -> {<<"application">>, <<"vnd.kahootz">>, []};
all_ext(<<"ktx">>) -> {<<"image">>, <<"ktx">>, []};
all_ext(<<"ktz">>) -> {<<"application">>, <<"vnd.kahootz">>, []};
all_ext(<<"kwd">>) -> {<<"application">>, <<"vnd.kde.kword">>, []};
all_ext(<<"kwt">>) -> {<<"application">>, <<"vnd.kde.kword">>, []};
all_ext(<<"lasxml">>) -> {<<"application">>, <<"vnd.las.las+xml">>, []};
all_ext(<<"latex">>) -> {<<"application">>, <<"x-latex">>, []};
all_ext(<<"lbd">>) -> {<<"application">>, <<"vnd.llamagraphics.life-balance.desktop">>, []};
all_ext(<<"lbe">>) -> {<<"application">>, <<"vnd.llamagraphics.life-balance.exchange+xml">>, []};
all_ext(<<"les">>) -> {<<"application">>, <<"vnd.hhe.lesson-player">>, []};
all_ext(<<"lha">>) -> {<<"application">>, <<"x-lzh-compressed">>, []};
all_ext(<<"link66">>) -> {<<"application">>, <<"vnd.route66.link66+xml">>, []};
all_ext(<<"list3820">>) -> {<<"application">>, <<"vnd.ibm.modcap">>, []};
all_ext(<<"listafp">>) -> {<<"application">>, <<"vnd.ibm.modcap">>, []};
all_ext(<<"list">>) -> {<<"text">>, <<"plain">>, []};
all_ext(<<"lnk">>) -> {<<"application">>, <<"x-ms-shortcut">>, []};
all_ext(<<"log">>) -> {<<"text">>, <<"plain">>, []};
all_ext(<<"lostxml">>) -> {<<"application">>, <<"lost+xml">>, []};
all_ext(<<"lrf">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"lrm">>) -> {<<"application">>, <<"vnd.ms-lrm">>, []};
all_ext(<<"ltf">>) -> {<<"application">>, <<"vnd.frogans.ltf">>, []};
all_ext(<<"lvp">>) -> {<<"audio">>, <<"vnd.lucent.voice">>, []};
all_ext(<<"lwp">>) -> {<<"application">>, <<"vnd.lotus-wordpro">>, []};
all_ext(<<"lzh">>) -> {<<"application">>, <<"x-lzh-compressed">>, []};
all_ext(<<"m13">>) -> {<<"application">>, <<"x-msmediaview">>, []};
all_ext(<<"m14">>) -> {<<"application">>, <<"x-msmediaview">>, []};
all_ext(<<"m1v">>) -> {<<"video">>, <<"mpeg">>, []};
all_ext(<<"m21">>) -> {<<"application">>, <<"mp21">>, []};
all_ext(<<"m2a">>) -> {<<"audio">>, <<"mpeg">>, []};
all_ext(<<"m2v">>) -> {<<"video">>, <<"mpeg">>, []};
all_ext(<<"m3a">>) -> {<<"audio">>, <<"mpeg">>, []};
all_ext(<<"m3u8">>) -> {<<"application">>, <<"vnd.apple.mpegurl">>, []};
all_ext(<<"m3u">>) -> {<<"audio">>, <<"x-mpegurl">>, []};
all_ext(<<"m4a">>) -> {<<"audio">>, <<"mp4">>, []};
all_ext(<<"m4u">>) -> {<<"video">>, <<"vnd.mpegurl">>, []};
all_ext(<<"m4v">>) -> {<<"video">>, <<"x-m4v">>, []};
all_ext(<<"ma">>) -> {<<"application">>, <<"mathematica">>, []};
all_ext(<<"mads">>) -> {<<"application">>, <<"mads+xml">>, []};
all_ext(<<"mag">>) -> {<<"application">>, <<"vnd.ecowin.chart">>, []};
all_ext(<<"maker">>) -> {<<"application">>, <<"vnd.framemaker">>, []};
all_ext(<<"man">>) -> {<<"text">>, <<"troff">>, []};
all_ext(<<"mar">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"mathml">>) -> {<<"application">>, <<"mathml+xml">>, []};
all_ext(<<"mb">>) -> {<<"application">>, <<"mathematica">>, []};
all_ext(<<"mbk">>) -> {<<"application">>, <<"vnd.mobius.mbk">>, []};
all_ext(<<"mbox">>) -> {<<"application">>, <<"mbox">>, []};
all_ext(<<"mc1">>) -> {<<"application">>, <<"vnd.medcalcdata">>, []};
all_ext(<<"mcd">>) -> {<<"application">>, <<"vnd.mcd">>, []};
all_ext(<<"mcurl">>) -> {<<"text">>, <<"vnd.curl.mcurl">>, []};
all_ext(<<"mdb">>) -> {<<"application">>, <<"x-msaccess">>, []};
all_ext(<<"mdi">>) -> {<<"image">>, <<"vnd.ms-modi">>, []};
all_ext(<<"mesh">>) -> {<<"model">>, <<"mesh">>, []};
all_ext(<<"meta4">>) -> {<<"application">>, <<"metalink4+xml">>, []};
all_ext(<<"metalink">>) -> {<<"application">>, <<"metalink+xml">>, []};
all_ext(<<"me">>) -> {<<"text">>, <<"troff">>, []};
all_ext(<<"mets">>) -> {<<"application">>, <<"mets+xml">>, []};
all_ext(<<"mfm">>) -> {<<"application">>, <<"vnd.mfmp">>, []};
all_ext(<<"mft">>) -> {<<"application">>, <<"rpki-manifest">>, []};
all_ext(<<"mgp">>) -> {<<"application">>, <<"vnd.osgeo.mapguide.package">>, []};
all_ext(<<"mgz">>) -> {<<"application">>, <<"vnd.proteus.magazine">>, []};
all_ext(<<"mid">>) -> {<<"audio">>, <<"midi">>, []};
all_ext(<<"midi">>) -> {<<"audio">>, <<"midi">>, []};
all_ext(<<"mie">>) -> {<<"application">>, <<"x-mie">>, []};
all_ext(<<"mif">>) -> {<<"application">>, <<"vnd.mif">>, []};
all_ext(<<"mime">>) -> {<<"message">>, <<"rfc822">>, []};
all_ext(<<"mj2">>) -> {<<"video">>, <<"mj2">>, []};
all_ext(<<"mjp2">>) -> {<<"video">>, <<"mj2">>, []};
all_ext(<<"mk3d">>) -> {<<"video">>, <<"x-matroska">>, []};
all_ext(<<"mka">>) -> {<<"audio">>, <<"x-matroska">>, []};
all_ext(<<"mks">>) -> {<<"video">>, <<"x-matroska">>, []};
all_ext(<<"mkv">>) -> {<<"video">>, <<"x-matroska">>, []};
all_ext(<<"mlp">>) -> {<<"application">>, <<"vnd.dolby.mlp">>, []};
all_ext(<<"mmd">>) -> {<<"application">>, <<"vnd.chipnuts.karaoke-mmd">>, []};
all_ext(<<"mmf">>) -> {<<"application">>, <<"vnd.smaf">>, []};
all_ext(<<"mmr">>) -> {<<"image">>, <<"vnd.fujixerox.edmics-mmr">>, []};
all_ext(<<"mng">>) -> {<<"video">>, <<"x-mng">>, []};
all_ext(<<"mny">>) -> {<<"application">>, <<"x-msmoney">>, []};
all_ext(<<"mobi">>) -> {<<"application">>, <<"x-mobipocket-ebook">>, []};
all_ext(<<"mods">>) -> {<<"application">>, <<"mods+xml">>, []};
all_ext(<<"movie">>) -> {<<"video">>, <<"x-sgi-movie">>, []};
all_ext(<<"mov">>) -> {<<"video">>, <<"quicktime">>, []};
all_ext(<<"mp21">>) -> {<<"application">>, <<"mp21">>, []};
all_ext(<<"mp2a">>) -> {<<"audio">>, <<"mpeg">>, []};
all_ext(<<"mp2">>) -> {<<"audio">>, <<"mpeg">>, []};
all_ext(<<"mp3">>) -> {<<"audio">>, <<"mpeg">>, []};
all_ext(<<"mp4a">>) -> {<<"audio">>, <<"mp4">>, []};
all_ext(<<"mp4s">>) -> {<<"application">>, <<"mp4">>, []};
all_ext(<<"mp4">>) -> {<<"video">>, <<"mp4">>, []};
all_ext(<<"mp4v">>) -> {<<"video">>, <<"mp4">>, []};
all_ext(<<"mpc">>) -> {<<"application">>, <<"vnd.mophun.certificate">>, []};
all_ext(<<"mpeg">>) -> {<<"video">>, <<"mpeg">>, []};
all_ext(<<"mpe">>) -> {<<"video">>, <<"mpeg">>, []};
all_ext(<<"mpg4">>) -> {<<"video">>, <<"mp4">>, []};
all_ext(<<"mpga">>) -> {<<"audio">>, <<"mpeg">>, []};
all_ext(<<"mpg">>) -> {<<"video">>, <<"mpeg">>, []};
all_ext(<<"mpkg">>) -> {<<"application">>, <<"vnd.apple.installer+xml">>, []};
all_ext(<<"mpm">>) -> {<<"application">>, <<"vnd.blueice.multipass">>, []};
all_ext(<<"mpn">>) -> {<<"application">>, <<"vnd.mophun.application">>, []};
all_ext(<<"mpp">>) -> {<<"application">>, <<"vnd.ms-project">>, []};
all_ext(<<"mpt">>) -> {<<"application">>, <<"vnd.ms-project">>, []};
all_ext(<<"mpy">>) -> {<<"application">>, <<"vnd.ibm.minipay">>, []};
all_ext(<<"mqy">>) -> {<<"application">>, <<"vnd.mobius.mqy">>, []};
all_ext(<<"mrc">>) -> {<<"application">>, <<"marc">>, []};
all_ext(<<"mrcx">>) -> {<<"application">>, <<"marcxml+xml">>, []};
all_ext(<<"mscml">>) -> {<<"application">>, <<"mediaservercontrol+xml">>, []};
all_ext(<<"mseed">>) -> {<<"application">>, <<"vnd.fdsn.mseed">>, []};
all_ext(<<"mseq">>) -> {<<"application">>, <<"vnd.mseq">>, []};
all_ext(<<"msf">>) -> {<<"application">>, <<"vnd.epson.msf">>, []};
all_ext(<<"msh">>) -> {<<"model">>, <<"mesh">>, []};
all_ext(<<"msi">>) -> {<<"application">>, <<"x-msdownload">>, []};
all_ext(<<"msl">>) -> {<<"application">>, <<"vnd.mobius.msl">>, []};
all_ext(<<"ms">>) -> {<<"text">>, <<"troff">>, []};
all_ext(<<"msty">>) -> {<<"application">>, <<"vnd.muvee.style">>, []};
all_ext(<<"mts">>) -> {<<"model">>, <<"vnd.mts">>, []};
all_ext(<<"mus">>) -> {<<"application">>, <<"vnd.musician">>, []};
all_ext(<<"musicxml">>) -> {<<"application">>, <<"vnd.recordare.musicxml+xml">>, []};
all_ext(<<"mvb">>) -> {<<"application">>, <<"x-msmediaview">>, []};
all_ext(<<"mwf">>) -> {<<"application">>, <<"vnd.mfer">>, []};
all_ext(<<"mxf">>) -> {<<"application">>, <<"mxf">>, []};
all_ext(<<"mxl">>) -> {<<"application">>, <<"vnd.recordare.musicxml">>, []};
all_ext(<<"mxml">>) -> {<<"application">>, <<"xv+xml">>, []};
all_ext(<<"mxs">>) -> {<<"application">>, <<"vnd.triscape.mxs">>, []};
all_ext(<<"mxu">>) -> {<<"video">>, <<"vnd.mpegurl">>, []};
all_ext(<<"n3">>) -> {<<"text">>, <<"n3">>, []};
all_ext(<<"nb">>) -> {<<"application">>, <<"mathematica">>, []};
all_ext(<<"nbp">>) -> {<<"application">>, <<"vnd.wolfram.player">>, []};
all_ext(<<"nc">>) -> {<<"application">>, <<"x-netcdf">>, []};
all_ext(<<"ncx">>) -> {<<"application">>, <<"x-dtbncx+xml">>, []};
all_ext(<<"nfo">>) -> {<<"text">>, <<"x-nfo">>, []};
all_ext(<<"n-gage">>) -> {<<"application">>, <<"vnd.nokia.n-gage.symbian.install">>, []};
all_ext(<<"ngdat">>) -> {<<"application">>, <<"vnd.nokia.n-gage.data">>, []};
all_ext(<<"nitf">>) -> {<<"application">>, <<"vnd.nitf">>, []};
all_ext(<<"nlu">>) -> {<<"application">>, <<"vnd.neurolanguage.nlu">>, []};
all_ext(<<"nml">>) -> {<<"application">>, <<"vnd.enliven">>, []};
all_ext(<<"nnd">>) -> {<<"application">>, <<"vnd.noblenet-directory">>, []};
all_ext(<<"nns">>) -> {<<"application">>, <<"vnd.noblenet-sealer">>, []};
all_ext(<<"nnw">>) -> {<<"application">>, <<"vnd.noblenet-web">>, []};
all_ext(<<"npx">>) -> {<<"image">>, <<"vnd.net-fpx">>, []};
all_ext(<<"nsc">>) -> {<<"application">>, <<"x-conference">>, []};
all_ext(<<"nsf">>) -> {<<"application">>, <<"vnd.lotus-notes">>, []};
all_ext(<<"ntf">>) -> {<<"application">>, <<"vnd.nitf">>, []};
all_ext(<<"nzb">>) -> {<<"application">>, <<"x-nzb">>, []};
all_ext(<<"oa2">>) -> {<<"application">>, <<"vnd.fujitsu.oasys2">>, []};
all_ext(<<"oa3">>) -> {<<"application">>, <<"vnd.fujitsu.oasys3">>, []};
all_ext(<<"oas">>) -> {<<"application">>, <<"vnd.fujitsu.oasys">>, []};
all_ext(<<"obd">>) -> {<<"application">>, <<"x-msbinder">>, []};
all_ext(<<"obj">>) -> {<<"application">>, <<"x-tgif">>, []};
all_ext(<<"oda">>) -> {<<"application">>, <<"oda">>, []};
all_ext(<<"odb">>) -> {<<"application">>, <<"vnd.oasis.opendocument.database">>, []};
all_ext(<<"odc">>) -> {<<"application">>, <<"vnd.oasis.opendocument.chart">>, []};
all_ext(<<"odf">>) -> {<<"application">>, <<"vnd.oasis.opendocument.formula">>, []};
all_ext(<<"odft">>) -> {<<"application">>, <<"vnd.oasis.opendocument.formula-template">>, []};
all_ext(<<"odg">>) -> {<<"application">>, <<"vnd.oasis.opendocument.graphics">>, []};
all_ext(<<"odi">>) -> {<<"application">>, <<"vnd.oasis.opendocument.image">>, []};
all_ext(<<"odm">>) -> {<<"application">>, <<"vnd.oasis.opendocument.text-master">>, []};
all_ext(<<"odp">>) -> {<<"application">>, <<"vnd.oasis.opendocument.presentation">>, []};
all_ext(<<"ods">>) -> {<<"application">>, <<"vnd.oasis.opendocument.spreadsheet">>, []};
all_ext(<<"odt">>) -> {<<"application">>, <<"vnd.oasis.opendocument.text">>, []};
all_ext(<<"oga">>) -> {<<"audio">>, <<"ogg">>, []};
all_ext(<<"ogg">>) -> {<<"audio">>, <<"ogg">>, []};
all_ext(<<"ogv">>) -> {<<"video">>, <<"ogg">>, []};
all_ext(<<"ogx">>) -> {<<"application">>, <<"ogg">>, []};
all_ext(<<"omdoc">>) -> {<<"application">>, <<"omdoc+xml">>, []};
all_ext(<<"onepkg">>) -> {<<"application">>, <<"onenote">>, []};
all_ext(<<"onetmp">>) -> {<<"application">>, <<"onenote">>, []};
all_ext(<<"onetoc2">>) -> {<<"application">>, <<"onenote">>, []};
all_ext(<<"onetoc">>) -> {<<"application">>, <<"onenote">>, []};
all_ext(<<"opf">>) -> {<<"application">>, <<"oebps-package+xml">>, []};
all_ext(<<"opml">>) -> {<<"text">>, <<"x-opml">>, []};
all_ext(<<"oprc">>) -> {<<"application">>, <<"vnd.palm">>, []};
all_ext(<<"org">>) -> {<<"application">>, <<"vnd.lotus-organizer">>, []};
all_ext(<<"osf">>) -> {<<"application">>, <<"vnd.yamaha.openscoreformat">>, []};
all_ext(<<"osfpvg">>) -> {<<"application">>, <<"vnd.yamaha.openscoreformat.osfpvg+xml">>, []};
all_ext(<<"otc">>) -> {<<"application">>, <<"vnd.oasis.opendocument.chart-template">>, []};
all_ext(<<"otf">>) -> {<<"font">>, <<"otf">>, []};
all_ext(<<"otg">>) -> {<<"application">>, <<"vnd.oasis.opendocument.graphics-template">>, []};
all_ext(<<"oth">>) -> {<<"application">>, <<"vnd.oasis.opendocument.text-web">>, []};
all_ext(<<"oti">>) -> {<<"application">>, <<"vnd.oasis.opendocument.image-template">>, []};
all_ext(<<"otp">>) -> {<<"application">>, <<"vnd.oasis.opendocument.presentation-template">>, []};
all_ext(<<"ots">>) -> {<<"application">>, <<"vnd.oasis.opendocument.spreadsheet-template">>, []};
all_ext(<<"ott">>) -> {<<"application">>, <<"vnd.oasis.opendocument.text-template">>, []};
all_ext(<<"oxps">>) -> {<<"application">>, <<"oxps">>, []};
all_ext(<<"oxt">>) -> {<<"application">>, <<"vnd.openofficeorg.extension">>, []};
all_ext(<<"p10">>) -> {<<"application">>, <<"pkcs10">>, []};
all_ext(<<"p12">>) -> {<<"application">>, <<"x-pkcs12">>, []};
all_ext(<<"p7b">>) -> {<<"application">>, <<"x-pkcs7-certificates">>, []};
all_ext(<<"p7c">>) -> {<<"application">>, <<"pkcs7-mime">>, []};
all_ext(<<"p7m">>) -> {<<"application">>, <<"pkcs7-mime">>, []};
all_ext(<<"p7r">>) -> {<<"application">>, <<"x-pkcs7-certreqresp">>, []};
all_ext(<<"p7s">>) -> {<<"application">>, <<"pkcs7-signature">>, []};
all_ext(<<"p8">>) -> {<<"application">>, <<"pkcs8">>, []};
all_ext(<<"pas">>) -> {<<"text">>, <<"x-pascal">>, []};
all_ext(<<"paw">>) -> {<<"application">>, <<"vnd.pawaafile">>, []};
all_ext(<<"pbd">>) -> {<<"application">>, <<"vnd.powerbuilder6">>, []};
all_ext(<<"pbm">>) -> {<<"image">>, <<"x-portable-bitmap">>, []};
all_ext(<<"pcap">>) -> {<<"application">>, <<"vnd.tcpdump.pcap">>, []};
all_ext(<<"pcf">>) -> {<<"application">>, <<"x-font-pcf">>, []};
all_ext(<<"pcl">>) -> {<<"application">>, <<"vnd.hp-pcl">>, []};
all_ext(<<"pclxl">>) -> {<<"application">>, <<"vnd.hp-pclxl">>, []};
all_ext(<<"pct">>) -> {<<"image">>, <<"x-pict">>, []};
all_ext(<<"pcurl">>) -> {<<"application">>, <<"vnd.curl.pcurl">>, []};
all_ext(<<"pcx">>) -> {<<"image">>, <<"x-pcx">>, []};
all_ext(<<"pdb">>) -> {<<"application">>, <<"vnd.palm">>, []};
all_ext(<<"pdf">>) -> {<<"application">>, <<"pdf">>, []};
all_ext(<<"pfa">>) -> {<<"application">>, <<"x-font-type1">>, []};
all_ext(<<"pfb">>) -> {<<"application">>, <<"x-font-type1">>, []};
all_ext(<<"pfm">>) -> {<<"application">>, <<"x-font-type1">>, []};
all_ext(<<"pfr">>) -> {<<"application">>, <<"font-tdpfr">>, []};
all_ext(<<"pfx">>) -> {<<"application">>, <<"x-pkcs12">>, []};
all_ext(<<"pgm">>) -> {<<"image">>, <<"x-portable-graymap">>, []};
all_ext(<<"pgn">>) -> {<<"application">>, <<"x-chess-pgn">>, []};
all_ext(<<"pgp">>) -> {<<"application">>, <<"pgp-encrypted">>, []};
all_ext(<<"pic">>) -> {<<"image">>, <<"x-pict">>, []};
all_ext(<<"pkg">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"pki">>) -> {<<"application">>, <<"pkixcmp">>, []};
all_ext(<<"pkipath">>) -> {<<"application">>, <<"pkix-pkipath">>, []};
all_ext(<<"plb">>) -> {<<"application">>, <<"vnd.3gpp.pic-bw-large">>, []};
all_ext(<<"plc">>) -> {<<"application">>, <<"vnd.mobius.plc">>, []};
all_ext(<<"plf">>) -> {<<"application">>, <<"vnd.pocketlearn">>, []};
all_ext(<<"pls">>) -> {<<"application">>, <<"pls+xml">>, []};
all_ext(<<"pml">>) -> {<<"application">>, <<"vnd.ctc-posml">>, []};
all_ext(<<"png">>) -> {<<"image">>, <<"png">>, []};
all_ext(<<"pnm">>) -> {<<"image">>, <<"x-portable-anymap">>, []};
all_ext(<<"portpkg">>) -> {<<"application">>, <<"vnd.macports.portpkg">>, []};
all_ext(<<"pot">>) -> {<<"application">>, <<"vnd.ms-powerpoint">>, []};
all_ext(<<"potm">>) -> {<<"application">>, <<"vnd.ms-powerpoint.template.macroenabled.12">>, []};
all_ext(<<"potx">>) -> {<<"application">>, <<"vnd.openxmlformats-officedocument.presentationml.template">>, []};
all_ext(<<"ppam">>) -> {<<"application">>, <<"vnd.ms-powerpoint.addin.macroenabled.12">>, []};
all_ext(<<"ppd">>) -> {<<"application">>, <<"vnd.cups-ppd">>, []};
all_ext(<<"ppm">>) -> {<<"image">>, <<"x-portable-pixmap">>, []};
all_ext(<<"pps">>) -> {<<"application">>, <<"vnd.ms-powerpoint">>, []};
all_ext(<<"ppsm">>) -> {<<"application">>, <<"vnd.ms-powerpoint.slideshow.macroenabled.12">>, []};
all_ext(<<"ppsx">>) -> {<<"application">>, <<"vnd.openxmlformats-officedocument.presentationml.slideshow">>, []};
all_ext(<<"ppt">>) -> {<<"application">>, <<"vnd.ms-powerpoint">>, []};
all_ext(<<"pptm">>) -> {<<"application">>, <<"vnd.ms-powerpoint.presentation.macroenabled.12">>, []};
all_ext(<<"pptx">>) -> {<<"application">>, <<"vnd.openxmlformats-officedocument.presentationml.presentation">>, []};
all_ext(<<"pqa">>) -> {<<"application">>, <<"vnd.palm">>, []};
all_ext(<<"prc">>) -> {<<"application">>, <<"x-mobipocket-ebook">>, []};
all_ext(<<"pre">>) -> {<<"application">>, <<"vnd.lotus-freelance">>, []};
all_ext(<<"prf">>) -> {<<"application">>, <<"pics-rules">>, []};
all_ext(<<"ps">>) -> {<<"application">>, <<"postscript">>, []};
all_ext(<<"psb">>) -> {<<"application">>, <<"vnd.3gpp.pic-bw-small">>, []};
all_ext(<<"psd">>) -> {<<"image">>, <<"vnd.adobe.photoshop">>, []};
all_ext(<<"psf">>) -> {<<"application">>, <<"x-font-linux-psf">>, []};
all_ext(<<"pskcxml">>) -> {<<"application">>, <<"pskc+xml">>, []};
all_ext(<<"p">>) -> {<<"text">>, <<"x-pascal">>, []};
all_ext(<<"ptid">>) -> {<<"application">>, <<"vnd.pvi.ptid1">>, []};
all_ext(<<"pub">>) -> {<<"application">>, <<"x-mspublisher">>, []};
all_ext(<<"pvb">>) -> {<<"application">>, <<"vnd.3gpp.pic-bw-var">>, []};
all_ext(<<"pwn">>) -> {<<"application">>, <<"vnd.3m.post-it-notes">>, []};
all_ext(<<"pya">>) -> {<<"audio">>, <<"vnd.ms-playready.media.pya">>, []};
all_ext(<<"pyv">>) -> {<<"video">>, <<"vnd.ms-playready.media.pyv">>, []};
all_ext(<<"qam">>) -> {<<"application">>, <<"vnd.epson.quickanime">>, []};
all_ext(<<"qbo">>) -> {<<"application">>, <<"vnd.intu.qbo">>, []};
all_ext(<<"qfx">>) -> {<<"application">>, <<"vnd.intu.qfx">>, []};
all_ext(<<"qps">>) -> {<<"application">>, <<"vnd.publishare-delta-tree">>, []};
all_ext(<<"qt">>) -> {<<"video">>, <<"quicktime">>, []};
all_ext(<<"qwd">>) -> {<<"application">>, <<"vnd.quark.quarkxpress">>, []};
all_ext(<<"qwt">>) -> {<<"application">>, <<"vnd.quark.quarkxpress">>, []};
all_ext(<<"qxb">>) -> {<<"application">>, <<"vnd.quark.quarkxpress">>, []};
all_ext(<<"qxd">>) -> {<<"application">>, <<"vnd.quark.quarkxpress">>, []};
all_ext(<<"qxl">>) -> {<<"application">>, <<"vnd.quark.quarkxpress">>, []};
all_ext(<<"qxt">>) -> {<<"application">>, <<"vnd.quark.quarkxpress">>, []};
all_ext(<<"ra">>) -> {<<"audio">>, <<"x-pn-realaudio">>, []};
all_ext(<<"ram">>) -> {<<"audio">>, <<"x-pn-realaudio">>, []};
all_ext(<<"rar">>) -> {<<"application">>, <<"x-rar-compressed">>, []};
all_ext(<<"ras">>) -> {<<"image">>, <<"x-cmu-raster">>, []};
all_ext(<<"rcprofile">>) -> {<<"application">>, <<"vnd.ipunplugged.rcprofile">>, []};
all_ext(<<"rdf">>) -> {<<"application">>, <<"rdf+xml">>, []};
all_ext(<<"rdz">>) -> {<<"application">>, <<"vnd.data-vision.rdz">>, []};
all_ext(<<"rep">>) -> {<<"application">>, <<"vnd.businessobjects">>, []};
all_ext(<<"res">>) -> {<<"application">>, <<"x-dtbresource+xml">>, []};
all_ext(<<"rgb">>) -> {<<"image">>, <<"x-rgb">>, []};
all_ext(<<"rif">>) -> {<<"application">>, <<"reginfo+xml">>, []};
all_ext(<<"rip">>) -> {<<"audio">>, <<"vnd.rip">>, []};
all_ext(<<"ris">>) -> {<<"application">>, <<"x-research-info-systems">>, []};
all_ext(<<"rl">>) -> {<<"application">>, <<"resource-lists+xml">>, []};
all_ext(<<"rlc">>) -> {<<"image">>, <<"vnd.fujixerox.edmics-rlc">>, []};
all_ext(<<"rld">>) -> {<<"application">>, <<"resource-lists-diff+xml">>, []};
all_ext(<<"rm">>) -> {<<"application">>, <<"vnd.rn-realmedia">>, []};
all_ext(<<"rmi">>) -> {<<"audio">>, <<"midi">>, []};
all_ext(<<"rmp">>) -> {<<"audio">>, <<"x-pn-realaudio-plugin">>, []};
all_ext(<<"rms">>) -> {<<"application">>, <<"vnd.jcp.javame.midlet-rms">>, []};
all_ext(<<"rmvb">>) -> {<<"application">>, <<"vnd.rn-realmedia-vbr">>, []};
all_ext(<<"rnc">>) -> {<<"application">>, <<"relax-ng-compact-syntax">>, []};
all_ext(<<"roa">>) -> {<<"application">>, <<"rpki-roa">>, []};
all_ext(<<"roff">>) -> {<<"text">>, <<"troff">>, []};
all_ext(<<"rp9">>) -> {<<"application">>, <<"vnd.cloanto.rp9">>, []};
all_ext(<<"rpss">>) -> {<<"application">>, <<"vnd.nokia.radio-presets">>, []};
all_ext(<<"rpst">>) -> {<<"application">>, <<"vnd.nokia.radio-preset">>, []};
all_ext(<<"rq">>) -> {<<"application">>, <<"sparql-query">>, []};
all_ext(<<"rs">>) -> {<<"application">>, <<"rls-services+xml">>, []};
all_ext(<<"rsd">>) -> {<<"application">>, <<"rsd+xml">>, []};
all_ext(<<"rss">>) -> {<<"application">>, <<"rss+xml">>, []};
all_ext(<<"rtf">>) -> {<<"application">>, <<"rtf">>, []};
all_ext(<<"rtx">>) -> {<<"text">>, <<"richtext">>, []};
all_ext(<<"s3m">>) -> {<<"audio">>, <<"s3m">>, []};
all_ext(<<"saf">>) -> {<<"application">>, <<"vnd.yamaha.smaf-audio">>, []};
all_ext(<<"sbml">>) -> {<<"application">>, <<"sbml+xml">>, []};
all_ext(<<"sc">>) -> {<<"application">>, <<"vnd.ibm.secure-container">>, []};
all_ext(<<"scd">>) -> {<<"application">>, <<"x-msschedule">>, []};
all_ext(<<"scm">>) -> {<<"application">>, <<"vnd.lotus-screencam">>, []};
all_ext(<<"scq">>) -> {<<"application">>, <<"scvp-cv-request">>, []};
all_ext(<<"scs">>) -> {<<"application">>, <<"scvp-cv-response">>, []};
all_ext(<<"scurl">>) -> {<<"text">>, <<"vnd.curl.scurl">>, []};
all_ext(<<"sda">>) -> {<<"application">>, <<"vnd.stardivision.draw">>, []};
all_ext(<<"sdc">>) -> {<<"application">>, <<"vnd.stardivision.calc">>, []};
all_ext(<<"sdd">>) -> {<<"application">>, <<"vnd.stardivision.impress">>, []};
all_ext(<<"sdkd">>) -> {<<"application">>, <<"vnd.solent.sdkm+xml">>, []};
all_ext(<<"sdkm">>) -> {<<"application">>, <<"vnd.solent.sdkm+xml">>, []};
all_ext(<<"sdp">>) -> {<<"application">>, <<"sdp">>, []};
all_ext(<<"sdw">>) -> {<<"application">>, <<"vnd.stardivision.writer">>, []};
all_ext(<<"see">>) -> {<<"application">>, <<"vnd.seemail">>, []};
all_ext(<<"seed">>) -> {<<"application">>, <<"vnd.fdsn.seed">>, []};
all_ext(<<"sema">>) -> {<<"application">>, <<"vnd.sema">>, []};
all_ext(<<"semd">>) -> {<<"application">>, <<"vnd.semd">>, []};
all_ext(<<"semf">>) -> {<<"application">>, <<"vnd.semf">>, []};
all_ext(<<"ser">>) -> {<<"application">>, <<"java-serialized-object">>, []};
all_ext(<<"setpay">>) -> {<<"application">>, <<"set-payment-initiation">>, []};
all_ext(<<"setreg">>) -> {<<"application">>, <<"set-registration-initiation">>, []};
all_ext(<<"sfd-hdstx">>) -> {<<"application">>, <<"vnd.hydrostatix.sof-data">>, []};
all_ext(<<"sfs">>) -> {<<"application">>, <<"vnd.spotfire.sfs">>, []};
all_ext(<<"sfv">>) -> {<<"text">>, <<"x-sfv">>, []};
all_ext(<<"sgi">>) -> {<<"image">>, <<"sgi">>, []};
all_ext(<<"sgl">>) -> {<<"application">>, <<"vnd.stardivision.writer-global">>, []};
all_ext(<<"sgml">>) -> {<<"text">>, <<"sgml">>, []};
all_ext(<<"sgm">>) -> {<<"text">>, <<"sgml">>, []};
all_ext(<<"sh">>) -> {<<"application">>, <<"x-sh">>, []};
all_ext(<<"shar">>) -> {<<"application">>, <<"x-shar">>, []};
all_ext(<<"shf">>) -> {<<"application">>, <<"shf+xml">>, []};
all_ext(<<"sid">>) -> {<<"image">>, <<"x-mrsid-image">>, []};
all_ext(<<"sig">>) -> {<<"application">>, <<"pgp-signature">>, []};
all_ext(<<"sil">>) -> {<<"audio">>, <<"silk">>, []};
all_ext(<<"silo">>) -> {<<"model">>, <<"mesh">>, []};
all_ext(<<"sis">>) -> {<<"application">>, <<"vnd.symbian.install">>, []};
all_ext(<<"sisx">>) -> {<<"application">>, <<"vnd.symbian.install">>, []};
all_ext(<<"sit">>) -> {<<"application">>, <<"x-stuffit">>, []};
all_ext(<<"sitx">>) -> {<<"application">>, <<"x-stuffitx">>, []};
all_ext(<<"skd">>) -> {<<"application">>, <<"vnd.koan">>, []};
all_ext(<<"skm">>) -> {<<"application">>, <<"vnd.koan">>, []};
all_ext(<<"skp">>) -> {<<"application">>, <<"vnd.koan">>, []};
all_ext(<<"skt">>) -> {<<"application">>, <<"vnd.koan">>, []};
all_ext(<<"sldm">>) -> {<<"application">>, <<"vnd.ms-powerpoint.slide.macroenabled.12">>, []};
all_ext(<<"sldx">>) -> {<<"application">>, <<"vnd.openxmlformats-officedocument.presentationml.slide">>, []};
all_ext(<<"slt">>) -> {<<"application">>, <<"vnd.epson.salt">>, []};
all_ext(<<"sm">>) -> {<<"application">>, <<"vnd.stepmania.stepchart">>, []};
all_ext(<<"smf">>) -> {<<"application">>, <<"vnd.stardivision.math">>, []};
all_ext(<<"smi">>) -> {<<"application">>, <<"smil+xml">>, []};
all_ext(<<"smil">>) -> {<<"application">>, <<"smil+xml">>, []};
all_ext(<<"smv">>) -> {<<"video">>, <<"x-smv">>, []};
all_ext(<<"smzip">>) -> {<<"application">>, <<"vnd.stepmania.package">>, []};
all_ext(<<"snd">>) -> {<<"audio">>, <<"basic">>, []};
all_ext(<<"snf">>) -> {<<"application">>, <<"x-font-snf">>, []};
all_ext(<<"so">>) -> {<<"application">>, <<"octet-stream">>, []};
all_ext(<<"spc">>) -> {<<"application">>, <<"x-pkcs7-certificates">>, []};
all_ext(<<"spf">>) -> {<<"application">>, <<"vnd.yamaha.smaf-phrase">>, []};
all_ext(<<"spl">>) -> {<<"application">>, <<"x-futuresplash">>, []};
all_ext(<<"spot">>) -> {<<"text">>, <<"vnd.in3d.spot">>, []};
all_ext(<<"spp">>) -> {<<"application">>, <<"scvp-vp-response">>, []};
all_ext(<<"spq">>) -> {<<"application">>, <<"scvp-vp-request">>, []};
all_ext(<<"spx">>) -> {<<"audio">>, <<"ogg">>, []};
all_ext(<<"sql">>) -> {<<"application">>, <<"x-sql">>, []};
all_ext(<<"src">>) -> {<<"application">>, <<"x-wais-source">>, []};
all_ext(<<"srt">>) -> {<<"application">>, <<"x-subrip">>, []};
all_ext(<<"sru">>) -> {<<"application">>, <<"sru+xml">>, []};
all_ext(<<"srx">>) -> {<<"application">>, <<"sparql-results+xml">>, []};
all_ext(<<"ssdl">>) -> {<<"application">>, <<"ssdl+xml">>, []};
all_ext(<<"sse">>) -> {<<"application">>, <<"vnd.kodak-descriptor">>, []};
all_ext(<<"ssf">>) -> {<<"application">>, <<"vnd.epson.ssf">>, []};
all_ext(<<"ssml">>) -> {<<"application">>, <<"ssml+xml">>, []};
all_ext(<<"st">>) -> {<<"application">>, <<"vnd.sailingtracker.track">>, []};
all_ext(<<"stc">>) -> {<<"application">>, <<"vnd.sun.xml.calc.template">>, []};
all_ext(<<"std">>) -> {<<"application">>, <<"vnd.sun.xml.draw.template">>, []};
all_ext(<<"s">>) -> {<<"text">>, <<"x-asm">>, []};
all_ext(<<"stf">>) -> {<<"application">>, <<"vnd.wt.stf">>, []};
all_ext(<<"sti">>) -> {<<"application">>, <<"vnd.sun.xml.impress.template">>, []};
all_ext(<<"stk">>) -> {<<"application">>, <<"hyperstudio">>, []};
all_ext(<<"stl">>) -> {<<"application">>, <<"vnd.ms-pki.stl">>, []};
all_ext(<<"str">>) -> {<<"application">>, <<"vnd.pg.format">>, []};
all_ext(<<"stw">>) -> {<<"application">>, <<"vnd.sun.xml.writer.template">>, []};
all_ext(<<"sub">>) -> {<<"image">>, <<"vnd.dvb.subtitle">>, []};
all_ext(<<"sus">>) -> {<<"application">>, <<"vnd.sus-calendar">>, []};
all_ext(<<"susp">>) -> {<<"application">>, <<"vnd.sus-calendar">>, []};
all_ext(<<"sv4cpio">>) -> {<<"application">>, <<"x-sv4cpio">>, []};
all_ext(<<"sv4crc">>) -> {<<"application">>, <<"x-sv4crc">>, []};
all_ext(<<"svc">>) -> {<<"application">>, <<"vnd.dvb.service">>, []};
all_ext(<<"svd">>) -> {<<"application">>, <<"vnd.svd">>, []};
all_ext(<<"svg">>) -> {<<"image">>, <<"svg+xml">>, []};
all_ext(<<"svgz">>) -> {<<"image">>, <<"svg+xml">>, []};
all_ext(<<"swa">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"swf">>) -> {<<"application">>, <<"x-shockwave-flash">>, []};
all_ext(<<"swi">>) -> {<<"application">>, <<"vnd.aristanetworks.swi">>, []};
all_ext(<<"sxc">>) -> {<<"application">>, <<"vnd.sun.xml.calc">>, []};
all_ext(<<"sxd">>) -> {<<"application">>, <<"vnd.sun.xml.draw">>, []};
all_ext(<<"sxg">>) -> {<<"application">>, <<"vnd.sun.xml.writer.global">>, []};
all_ext(<<"sxi">>) -> {<<"application">>, <<"vnd.sun.xml.impress">>, []};
all_ext(<<"sxm">>) -> {<<"application">>, <<"vnd.sun.xml.math">>, []};
all_ext(<<"sxw">>) -> {<<"application">>, <<"vnd.sun.xml.writer">>, []};
all_ext(<<"t3">>) -> {<<"application">>, <<"x-t3vm-image">>, []};
all_ext(<<"taglet">>) -> {<<"application">>, <<"vnd.mynfc">>, []};
all_ext(<<"tao">>) -> {<<"application">>, <<"vnd.tao.intent-module-archive">>, []};
all_ext(<<"tar">>) -> {<<"application">>, <<"x-tar">>, []};
all_ext(<<"tcap">>) -> {<<"application">>, <<"vnd.3gpp2.tcap">>, []};
all_ext(<<"tcl">>) -> {<<"application">>, <<"x-tcl">>, []};
all_ext(<<"teacher">>) -> {<<"application">>, <<"vnd.smart.teacher">>, []};
all_ext(<<"tei">>) -> {<<"application">>, <<"tei+xml">>, []};
all_ext(<<"teicorpus">>) -> {<<"application">>, <<"tei+xml">>, []};
all_ext(<<"tex">>) -> {<<"application">>, <<"x-tex">>, []};
all_ext(<<"texi">>) -> {<<"application">>, <<"x-texinfo">>, []};
all_ext(<<"texinfo">>) -> {<<"application">>, <<"x-texinfo">>, []};
all_ext(<<"text">>) -> {<<"text">>, <<"plain">>, []};
all_ext(<<"tfi">>) -> {<<"application">>, <<"thraud+xml">>, []};
all_ext(<<"tfm">>) -> {<<"application">>, <<"x-tex-tfm">>, []};
all_ext(<<"tga">>) -> {<<"image">>, <<"x-tga">>, []};
all_ext(<<"thmx">>) -> {<<"application">>, <<"vnd.ms-officetheme">>, []};
all_ext(<<"tiff">>) -> {<<"image">>, <<"tiff">>, []};
all_ext(<<"tif">>) -> {<<"image">>, <<"tiff">>, []};
all_ext(<<"tmo">>) -> {<<"application">>, <<"vnd.tmobile-livetv">>, []};
all_ext(<<"torrent">>) -> {<<"application">>, <<"x-bittorrent">>, []};
all_ext(<<"tpl">>) -> {<<"application">>, <<"vnd.groove-tool-template">>, []};
all_ext(<<"tpt">>) -> {<<"application">>, <<"vnd.trid.tpt">>, []};
all_ext(<<"tra">>) -> {<<"application">>, <<"vnd.trueapp">>, []};
all_ext(<<"trm">>) -> {<<"application">>, <<"x-msterminal">>, []};
all_ext(<<"tr">>) -> {<<"text">>, <<"troff">>, []};
all_ext(<<"tsd">>) -> {<<"application">>, <<"timestamped-data">>, []};
all_ext(<<"tsv">>) -> {<<"text">>, <<"tab-separated-values">>, []};
all_ext(<<"ttc">>) -> {<<"font">>, <<"collection">>, []};
all_ext(<<"t">>) -> {<<"text">>, <<"troff">>, []};
all_ext(<<"ttf">>) -> {<<"font">>, <<"ttf">>, []};
all_ext(<<"ttl">>) -> {<<"text">>, <<"turtle">>, []};
all_ext(<<"twd">>) -> {<<"application">>, <<"vnd.simtech-mindmapper">>, []};
all_ext(<<"twds">>) -> {<<"application">>, <<"vnd.simtech-mindmapper">>, []};
all_ext(<<"txd">>) -> {<<"application">>, <<"vnd.genomatix.tuxedo">>, []};
all_ext(<<"txf">>) -> {<<"application">>, <<"vnd.mobius.txf">>, []};
all_ext(<<"txt">>) -> {<<"text">>, <<"plain">>, []};
all_ext(<<"u32">>) -> {<<"application">>, <<"x-authorware-bin">>, []};
all_ext(<<"udeb">>) -> {<<"application">>, <<"x-debian-package">>, []};
all_ext(<<"ufd">>) -> {<<"application">>, <<"vnd.ufdl">>, []};
all_ext(<<"ufdl">>) -> {<<"application">>, <<"vnd.ufdl">>, []};
all_ext(<<"ulx">>) -> {<<"application">>, <<"x-glulx">>, []};
all_ext(<<"umj">>) -> {<<"application">>, <<"vnd.umajin">>, []};
all_ext(<<"unityweb">>) -> {<<"application">>, <<"vnd.unity">>, []};
all_ext(<<"uoml">>) -> {<<"application">>, <<"vnd.uoml+xml">>, []};
all_ext(<<"uris">>) -> {<<"text">>, <<"uri-list">>, []};
all_ext(<<"uri">>) -> {<<"text">>, <<"uri-list">>, []};
all_ext(<<"urls">>) -> {<<"text">>, <<"uri-list">>, []};
all_ext(<<"ustar">>) -> {<<"application">>, <<"x-ustar">>, []};
all_ext(<<"utz">>) -> {<<"application">>, <<"vnd.uiq.theme">>, []};
all_ext(<<"uu">>) -> {<<"text">>, <<"x-uuencode">>, []};
all_ext(<<"uva">>) -> {<<"audio">>, <<"vnd.dece.audio">>, []};
all_ext(<<"uvd">>) -> {<<"application">>, <<"vnd.dece.data">>, []};
all_ext(<<"uvf">>) -> {<<"application">>, <<"vnd.dece.data">>, []};
all_ext(<<"uvg">>) -> {<<"image">>, <<"vnd.dece.graphic">>, []};
all_ext(<<"uvh">>) -> {<<"video">>, <<"vnd.dece.hd">>, []};
all_ext(<<"uvi">>) -> {<<"image">>, <<"vnd.dece.graphic">>, []};
all_ext(<<"uvm">>) -> {<<"video">>, <<"vnd.dece.mobile">>, []};
all_ext(<<"uvp">>) -> {<<"video">>, <<"vnd.dece.pd">>, []};
all_ext(<<"uvs">>) -> {<<"video">>, <<"vnd.dece.sd">>, []};
all_ext(<<"uvt">>) -> {<<"application">>, <<"vnd.dece.ttml+xml">>, []};
all_ext(<<"uvu">>) -> {<<"video">>, <<"vnd.uvvu.mp4">>, []};
all_ext(<<"uvva">>) -> {<<"audio">>, <<"vnd.dece.audio">>, []};
all_ext(<<"uvvd">>) -> {<<"application">>, <<"vnd.dece.data">>, []};
all_ext(<<"uvvf">>) -> {<<"application">>, <<"vnd.dece.data">>, []};
all_ext(<<"uvvg">>) -> {<<"image">>, <<"vnd.dece.graphic">>, []};
all_ext(<<"uvvh">>) -> {<<"video">>, <<"vnd.dece.hd">>, []};
all_ext(<<"uvvi">>) -> {<<"image">>, <<"vnd.dece.graphic">>, []};
all_ext(<<"uvvm">>) -> {<<"video">>, <<"vnd.dece.mobile">>, []};
all_ext(<<"uvvp">>) -> {<<"video">>, <<"vnd.dece.pd">>, []};
all_ext(<<"uvvs">>) -> {<<"video">>, <<"vnd.dece.sd">>, []};
all_ext(<<"uvvt">>) -> {<<"application">>, <<"vnd.dece.ttml+xml">>, []};
all_ext(<<"uvvu">>) -> {<<"video">>, <<"vnd.uvvu.mp4">>, []};
all_ext(<<"uvv">>) -> {<<"video">>, <<"vnd.dece.video">>, []};
all_ext(<<"uvvv">>) -> {<<"video">>, <<"vnd.dece.video">>, []};
all_ext(<<"uvvx">>) -> {<<"application">>, <<"vnd.dece.unspecified">>, []};
all_ext(<<"uvvz">>) -> {<<"application">>, <<"vnd.dece.zip">>, []};
all_ext(<<"uvx">>) -> {<<"application">>, <<"vnd.dece.unspecified">>, []};
all_ext(<<"uvz">>) -> {<<"application">>, <<"vnd.dece.zip">>, []};
all_ext(<<"vcard">>) -> {<<"text">>, <<"vcard">>, []};
all_ext(<<"vcd">>) -> {<<"application">>, <<"x-cdlink">>, []};
all_ext(<<"vcf">>) -> {<<"text">>, <<"x-vcard">>, []};
all_ext(<<"vcg">>) -> {<<"application">>, <<"vnd.groove-vcard">>, []};
all_ext(<<"vcs">>) -> {<<"text">>, <<"x-vcalendar">>, []};
all_ext(<<"vcx">>) -> {<<"application">>, <<"vnd.vcx">>, []};
all_ext(<<"vis">>) -> {<<"application">>, <<"vnd.visionary">>, []};
all_ext(<<"viv">>) -> {<<"video">>, <<"vnd.vivo">>, []};
all_ext(<<"vob">>) -> {<<"video">>, <<"x-ms-vob">>, []};
all_ext(<<"vor">>) -> {<<"application">>, <<"vnd.stardivision.writer">>, []};
all_ext(<<"vox">>) -> {<<"application">>, <<"x-authorware-bin">>, []};
all_ext(<<"vrml">>) -> {<<"model">>, <<"vrml">>, []};
all_ext(<<"vsd">>) -> {<<"application">>, <<"vnd.visio">>, []};
all_ext(<<"vsf">>) -> {<<"application">>, <<"vnd.vsf">>, []};
all_ext(<<"vss">>) -> {<<"application">>, <<"vnd.visio">>, []};
all_ext(<<"vst">>) -> {<<"application">>, <<"vnd.visio">>, []};
all_ext(<<"vsw">>) -> {<<"application">>, <<"vnd.visio">>, []};
all_ext(<<"vtu">>) -> {<<"model">>, <<"vnd.vtu">>, []};
all_ext(<<"vxml">>) -> {<<"application">>, <<"voicexml+xml">>, []};
all_ext(<<"w3d">>) -> {<<"application">>, <<"x-director">>, []};
all_ext(<<"wad">>) -> {<<"application">>, <<"x-doom">>, []};
all_ext(<<"wav">>) -> {<<"audio">>, <<"x-wav">>, []};
all_ext(<<"wax">>) -> {<<"audio">>, <<"x-ms-wax">>, []};
all_ext(<<"wbmp">>) -> {<<"image">>, <<"vnd.wap.wbmp">>, []};
all_ext(<<"wbs">>) -> {<<"application">>, <<"vnd.criticaltools.wbs+xml">>, []};
all_ext(<<"wbxml">>) -> {<<"application">>, <<"vnd.wap.wbxml">>, []};
all_ext(<<"wcm">>) -> {<<"application">>, <<"vnd.ms-works">>, []};
all_ext(<<"wdb">>) -> {<<"application">>, <<"vnd.ms-works">>, []};
all_ext(<<"wdp">>) -> {<<"image">>, <<"vnd.ms-photo">>, []};
all_ext(<<"weba">>) -> {<<"audio">>, <<"webm">>, []};
all_ext(<<"webm">>) -> {<<"video">>, <<"webm">>, []};
all_ext(<<"webp">>) -> {<<"image">>, <<"webp">>, []};
all_ext(<<"wg">>) -> {<<"application">>, <<"vnd.pmi.widget">>, []};
all_ext(<<"wgt">>) -> {<<"application">>, <<"widget">>, []};
all_ext(<<"wks">>) -> {<<"application">>, <<"vnd.ms-works">>, []};
all_ext(<<"wma">>) -> {<<"audio">>, <<"x-ms-wma">>, []};
all_ext(<<"wmd">>) -> {<<"application">>, <<"x-ms-wmd">>, []};
all_ext(<<"wmf">>) -> {<<"application">>, <<"x-msmetafile">>, []};
all_ext(<<"wmlc">>) -> {<<"application">>, <<"vnd.wap.wmlc">>, []};
all_ext(<<"wmlsc">>) -> {<<"application">>, <<"vnd.wap.wmlscriptc">>, []};
all_ext(<<"wmls">>) -> {<<"text">>, <<"vnd.wap.wmlscript">>, []};
all_ext(<<"wml">>) -> {<<"text">>, <<"vnd.wap.wml">>, []};
all_ext(<<"wm">>) -> {<<"video">>, <<"x-ms-wm">>, []};
all_ext(<<"wmv">>) -> {<<"video">>, <<"x-ms-wmv">>, []};
all_ext(<<"wmx">>) -> {<<"video">>, <<"x-ms-wmx">>, []};
all_ext(<<"wmz">>) -> {<<"application">>, <<"x-msmetafile">>, []};
all_ext(<<"woff2">>) -> {<<"font">>, <<"woff2">>, []};
all_ext(<<"woff">>) -> {<<"font">>, <<"woff">>, []};
all_ext(<<"wpd">>) -> {<<"application">>, <<"vnd.wordperfect">>, []};
all_ext(<<"wpl">>) -> {<<"application">>, <<"vnd.ms-wpl">>, []};
all_ext(<<"wps">>) -> {<<"application">>, <<"vnd.ms-works">>, []};
all_ext(<<"wqd">>) -> {<<"application">>, <<"vnd.wqd">>, []};
all_ext(<<"wri">>) -> {<<"application">>, <<"x-mswrite">>, []};
all_ext(<<"wrl">>) -> {<<"model">>, <<"vrml">>, []};
all_ext(<<"wsdl">>) -> {<<"application">>, <<"wsdl+xml">>, []};
all_ext(<<"wspolicy">>) -> {<<"application">>, <<"wspolicy+xml">>, []};
all_ext(<<"wtb">>) -> {<<"application">>, <<"vnd.webturbo">>, []};
all_ext(<<"wvx">>) -> {<<"video">>, <<"x-ms-wvx">>, []};
all_ext(<<"x32">>) -> {<<"application">>, <<"x-authorware-bin">>, []};
all_ext(<<"x3db">>) -> {<<"model">>, <<"x3d+binary">>, []};
all_ext(<<"x3dbz">>) -> {<<"model">>, <<"x3d+binary">>, []};
all_ext(<<"x3d">>) -> {<<"model">>, <<"x3d+xml">>, []};
all_ext(<<"x3dv">>) -> {<<"model">>, <<"x3d+vrml">>, []};
all_ext(<<"x3dvz">>) -> {<<"model">>, <<"x3d+vrml">>, []};
all_ext(<<"x3dz">>) -> {<<"model">>, <<"x3d+xml">>, []};
all_ext(<<"xaml">>) -> {<<"application">>, <<"xaml+xml">>, []};
all_ext(<<"xap">>) -> {<<"application">>, <<"x-silverlight-app">>, []};
all_ext(<<"xar">>) -> {<<"application">>, <<"vnd.xara">>, []};
all_ext(<<"xbap">>) -> {<<"application">>, <<"x-ms-xbap">>, []};
all_ext(<<"xbd">>) -> {<<"application">>, <<"vnd.fujixerox.docuworks.binder">>, []};
all_ext(<<"xbm">>) -> {<<"image">>, <<"x-xbitmap">>, []};
all_ext(<<"xdf">>) -> {<<"application">>, <<"xcap-diff+xml">>, []};
all_ext(<<"xdm">>) -> {<<"application">>, <<"vnd.syncml.dm+xml">>, []};
all_ext(<<"xdp">>) -> {<<"application">>, <<"vnd.adobe.xdp+xml">>, []};
all_ext(<<"xdssc">>) -> {<<"application">>, <<"dssc+xml">>, []};
all_ext(<<"xdw">>) -> {<<"application">>, <<"vnd.fujixerox.docuworks">>, []};
all_ext(<<"xenc">>) -> {<<"application">>, <<"xenc+xml">>, []};
all_ext(<<"xer">>) -> {<<"application">>, <<"patch-ops-error+xml">>, []};
all_ext(<<"xfdf">>) -> {<<"application">>, <<"vnd.adobe.xfdf">>, []};
all_ext(<<"xfdl">>) -> {<<"application">>, <<"vnd.xfdl">>, []};
all_ext(<<"xht">>) -> {<<"application">>, <<"xhtml+xml">>, []};
all_ext(<<"xhtml">>) -> {<<"application">>, <<"xhtml+xml">>, []};
all_ext(<<"xhvml">>) -> {<<"application">>, <<"xv+xml">>, []};
all_ext(<<"xif">>) -> {<<"image">>, <<"vnd.xiff">>, []};
all_ext(<<"xla">>) -> {<<"application">>, <<"vnd.ms-excel">>, []};
all_ext(<<"xlam">>) -> {<<"application">>, <<"vnd.ms-excel.addin.macroenabled.12">>, []};
all_ext(<<"xlc">>) -> {<<"application">>, <<"vnd.ms-excel">>, []};
all_ext(<<"xlf">>) -> {<<"application">>, <<"x-xliff+xml">>, []};
all_ext(<<"xlm">>) -> {<<"application">>, <<"vnd.ms-excel">>, []};
all_ext(<<"xls">>) -> {<<"application">>, <<"vnd.ms-excel">>, []};
all_ext(<<"xlsb">>) -> {<<"application">>, <<"vnd.ms-excel.sheet.binary.macroenabled.12">>, []};
all_ext(<<"xlsm">>) -> {<<"application">>, <<"vnd.ms-excel.sheet.macroenabled.12">>, []};
all_ext(<<"xlsx">>) -> {<<"application">>, <<"vnd.openxmlformats-officedocument.spreadsheetml.sheet">>, []};
all_ext(<<"xlt">>) -> {<<"application">>, <<"vnd.ms-excel">>, []};
all_ext(<<"xltm">>) -> {<<"application">>, <<"vnd.ms-excel.template.macroenabled.12">>, []};
all_ext(<<"xltx">>) -> {<<"application">>, <<"vnd.openxmlformats-officedocument.spreadsheetml.template">>, []};
all_ext(<<"xlw">>) -> {<<"application">>, <<"vnd.ms-excel">>, []};
all_ext(<<"xm">>) -> {<<"audio">>, <<"xm">>, []};
all_ext(<<"xml">>) -> {<<"application">>, <<"xml">>, []};
all_ext(<<"xo">>) -> {<<"application">>, <<"vnd.olpc-sugar">>, []};
all_ext(<<"xop">>) -> {<<"application">>, <<"xop+xml">>, []};
all_ext(<<"xpi">>) -> {<<"application">>, <<"x-xpinstall">>, []};
all_ext(<<"xpl">>) -> {<<"application">>, <<"xproc+xml">>, []};
all_ext(<<"xpm">>) -> {<<"image">>, <<"x-xpixmap">>, []};
all_ext(<<"xpr">>) -> {<<"application">>, <<"vnd.is-xpr">>, []};
all_ext(<<"xps">>) -> {<<"application">>, <<"vnd.ms-xpsdocument">>, []};
all_ext(<<"xpw">>) -> {<<"application">>, <<"vnd.intercon.formnet">>, []};
all_ext(<<"xpx">>) -> {<<"application">>, <<"vnd.intercon.formnet">>, []};
all_ext(<<"xsl">>) -> {<<"application">>, <<"xml">>, []};
all_ext(<<"xslt">>) -> {<<"application">>, <<"xslt+xml">>, []};
all_ext(<<"xsm">>) -> {<<"application">>, <<"vnd.syncml+xml">>, []};
all_ext(<<"xspf">>) -> {<<"application">>, <<"xspf+xml">>, []};
all_ext(<<"xul">>) -> {<<"application">>, <<"vnd.mozilla.xul+xml">>, []};
all_ext(<<"xvm">>) -> {<<"application">>, <<"xv+xml">>, []};
all_ext(<<"xvml">>) -> {<<"application">>, <<"xv+xml">>, []};
all_ext(<<"xwd">>) -> {<<"image">>, <<"x-xwindowdump">>, []};
all_ext(<<"xyz">>) -> {<<"chemical">>, <<"x-xyz">>, []};
all_ext(<<"xz">>) -> {<<"application">>, <<"x-xz">>, []};
all_ext(<<"yang">>) -> {<<"application">>, <<"yang">>, []};
all_ext(<<"yin">>) -> {<<"application">>, <<"yin+xml">>, []};
all_ext(<<"z1">>) -> {<<"application">>, <<"x-zmachine">>, []};
all_ext(<<"z2">>) -> {<<"application">>, <<"x-zmachine">>, []};
all_ext(<<"z3">>) -> {<<"application">>, <<"x-zmachine">>, []};
all_ext(<<"z4">>) -> {<<"application">>, <<"x-zmachine">>, []};
all_ext(<<"z5">>) -> {<<"application">>, <<"x-zmachine">>, []};
all_ext(<<"z6">>) -> {<<"application">>, <<"x-zmachine">>, []};
all_ext(<<"z7">>) -> {<<"application">>, <<"x-zmachine">>, []};
all_ext(<<"z8">>) -> {<<"application">>, <<"x-zmachine">>, []};
all_ext(<<"zaz">>) -> {<<"application">>, <<"vnd.zzazz.deck+xml">>, []};
all_ext(<<"zip">>) -> {<<"application">>, <<"zip">>, []};
all_ext(<<"zir">>) -> {<<"application">>, <<"vnd.zul">>, []};
all_ext(<<"zirz">>) -> {<<"application">>, <<"vnd.zul">>, []};
all_ext(<<"zmm">>) -> {<<"application">>, <<"vnd.handheld-entertainment+xml">>, []};
%% GENERATED
all_ext(_) -> {<<"application">>, <<"octet-stream">>, []}.

web_ext(<<"css">>) -> {<<"text">>, <<"css">>, []};
web_ext(<<"gif">>) -> {<<"image">>, <<"gif">>, []};
web_ext(<<"html">>) -> {<<"text">>, <<"html">>, []};
web_ext(<<"htm">>) -> {<<"text">>, <<"html">>, []};
web_ext(<<"ico">>) -> {<<"image">>, <<"x-icon">>, []};
web_ext(<<"jpeg">>) -> {<<"image">>, <<"jpeg">>, []};
web_ext(<<"jpg">>) -> {<<"image">>, <<"jpeg">>, []};
web_ext(<<"js">>) -> {<<"application">>, <<"javascript">>, []};
web_ext(<<"mp3">>) -> {<<"audio">>, <<"mpeg">>, []};
web_ext(<<"mp4">>) -> {<<"video">>, <<"mp4">>, []};
web_ext(<<"ogg">>) -> {<<"audio">>, <<"ogg">>, []};
web_ext(<<"ogv">>) -> {<<"video">>, <<"ogg">>, []};
web_ext(<<"png">>) -> {<<"image">>, <<"png">>, []};
web_ext(<<"svg">>) -> {<<"image">>, <<"svg+xml">>, []};
web_ext(<<"wav">>) -> {<<"audio">>, <<"x-wav">>, []};
web_ext(<<"webm">>) -> {<<"video">>, <<"webm">>, []};
web_ext(_) -> {<<"application">>, <<"octet-stream">>, []}.

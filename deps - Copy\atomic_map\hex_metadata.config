{<<"app">>,<<"atomic_map">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,
 <<"A small utility to convert deep Elixir maps with mixed string/atom keys to atom-only keyed maps">>}.
{<<"elixir">>,<<">= 1.2.0">>}.
{<<"files">>,
 [<<"lib/atomic_map.ex">>,<<"mix.exs">>,<<"README.md">>,<<"CHANGELOG.md">>]}.
{<<"licenses">>,[<<"MIT License">>]}.
{<<"links">>,
 [{<<"docs">>,<<"http://hexdocs.pm/atomic_map/0.9.3/">>},
  {<<"github">>,<<"https://github.com/ruby2elixir/atomic_map">>}]}.
{<<"maintainers">>,[<<"Roman Heinrich">>]}.
{<<"name">>,<<"atomic_map">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"0.9.3">>}.

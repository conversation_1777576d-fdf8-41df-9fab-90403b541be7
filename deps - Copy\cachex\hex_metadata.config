{<<"app">>,<<"cachex">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,<<"Powerful in-memory key/value storage for Elixir">>}.
{<<"elixir">>,<<"~> 1.5">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/cachex.ex">>,<<"lib/cachex">>,
  <<"lib/cachex/execution_error.ex">>,<<"lib/cachex/warmer.ex">>,
  <<"lib/cachex/spec.ex">>,<<"lib/cachex/policy.ex">>,
  <<"lib/cachex/actions.ex">>,<<"lib/cachex/hook.ex">>,
  <<"lib/cachex/options.ex">>,<<"lib/cachex/services.ex">>,
  <<"lib/cachex/disk.ex">>,<<"lib/cachex/spec">>,
  <<"lib/cachex/spec/validator.ex">>,<<"lib/cachex/query.ex">>,
  <<"lib/cachex/router.ex">>,<<"lib/cachex/errors.ex">>,
  <<"lib/cachex/actions">>,<<"lib/cachex/actions/transaction.ex">>,
  <<"lib/cachex/actions/reset.ex">>,<<"lib/cachex/actions/empty.ex">>,
  <<"lib/cachex/actions/purge.ex">>,<<"lib/cachex/actions/keys.ex">>,
  <<"lib/cachex/actions/put_many.ex">>,<<"lib/cachex/actions/stream.ex">>,
  <<"lib/cachex/actions/size.ex">>,<<"lib/cachex/actions/export.ex">>,
  <<"lib/cachex/actions/import.ex">>,<<"lib/cachex/actions/fetch.ex">>,
  <<"lib/cachex/actions/update.ex">>,<<"lib/cachex/actions/put.ex">>,
  <<"lib/cachex/actions/exists.ex">>,<<"lib/cachex/actions/del.ex">>,
  <<"lib/cachex/actions/clear.ex">>,<<"lib/cachex/actions/incr.ex">>,
  <<"lib/cachex/actions/invoke.ex">>,<<"lib/cachex/actions/load.ex">>,
  <<"lib/cachex/actions/get.ex">>,<<"lib/cachex/actions/get_and_update.ex">>,
  <<"lib/cachex/actions/count.ex">>,<<"lib/cachex/actions/inspect.ex">>,
  <<"lib/cachex/actions/expire.ex">>,<<"lib/cachex/actions/dump.ex">>,
  <<"lib/cachex/actions/ttl.ex">>,<<"lib/cachex/actions/stats.ex">>,
  <<"lib/cachex/actions/refresh.ex">>,<<"lib/cachex/actions/take.ex">>,
  <<"lib/cachex/actions/touch.ex">>,<<"lib/cachex/application.ex">>,
  <<"lib/cachex/stats.ex">>,<<"lib/cachex/services">>,
  <<"lib/cachex/services/courier.ex">>,<<"lib/cachex/services/locksmith.ex">>,
  <<"lib/cachex/services/janitor.ex">>,<<"lib/cachex/services/locksmith">>,
  <<"lib/cachex/services/locksmith/queue.ex">>,
  <<"lib/cachex/services/overseer.ex">>,
  <<"lib/cachex/services/informant.ex">>,
  <<"lib/cachex/services/incubator.ex">>,<<"lib/cachex/policy">>,
  <<"lib/cachex/policy/lrw.ex">>,<<"lib/cachex/policy/lrw">>,
  <<"lib/cachex/policy/lrw/evented.ex">>,
  <<"lib/cachex/policy/lrw/scheduled.ex">>,<<"mix.exs">>,<<"LICENSE">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,
 [{<<"Docs">>,<<"http://hexdocs.pm/cachex">>},
  {<<"GitHub">>,<<"https://github.com/whitfin/cachex">>}]}.
{<<"name">>,<<"cachex">>}.
{<<"requirements">>,
 [[{<<"app">>,<<"eternal">>},
   {<<"name">>,<<"eternal">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.2">>}],
  [{<<"app">>,<<"jumper">>},
   {<<"name">>,<<"jumper">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.0">>}],
  [{<<"app">>,<<"sleeplocks">>},
   {<<"name">>,<<"sleeplocks">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.1">>}],
  [{<<"app">>,<<"unsafe">>},
   {<<"name">>,<<"unsafe">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.0">>}]]}.
{<<"version">>,<<"3.6.0">>}.

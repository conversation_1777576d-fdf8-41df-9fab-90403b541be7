{<<"links">>,
 [{<<"Function reference">>,
   <<"https://ninenines.eu/docs/en/cowlib/2.13/manual/">>},
  {<<"GitHub">>,<<"https://github.com/ninenines/cowlib">>},
  {<<"Sponsor">>,<<"https://github.com/sponsors/essen">>}]}.
{<<"name">>,<<"cowlib">>}.
{<<"version">>,<<"2.13.0">>}.
{<<"description">>,<<"Support library for manipulating Web protocols.">>}.
{<<"app">>,<<"cowlib">>}.
{<<"build_tools">>,[<<"make">>,<<"rebar3">>]}.
{<<"files">>,
 [<<"ebin/cowlib.app">>,<<"erlang.mk">>,<<"include/cow_inline.hrl">>,
  <<"include/cow_parse.hrl">>,<<"LICENSE">>,<<"Makefile">>,
  <<"README.asciidoc">>,<<"src/cow_base64url.erl">>,<<"src/cow_cookie.erl">>,
  <<"src/cow_date.erl">>,<<"src/cow_hpack.erl">>,
  <<"src/cow_hpack_dec_huffman_lookup.hrl">>,<<"src/cow_http.erl">>,
  <<"src/cow_http2.erl">>,<<"src/cow_http2_machine.erl">>,
  <<"src/cow_http_hd.erl">>,<<"src/cow_http_struct_hd.erl">>,
  <<"src/cow_http_te.erl">>,<<"src/cow_iolists.erl">>,<<"src/cow_link.erl">>,
  <<"src/cow_mimetypes.erl">>,<<"src/cow_mimetypes.erl.src">>,
  <<"src/cow_multipart.erl">>,<<"src/cow_qs.erl">>,<<"src/cow_spdy.erl">>,
  <<"src/cow_spdy.hrl">>,<<"src/cow_sse.erl">>,<<"src/cow_uri.erl">>,
  <<"src/cow_uri_template.erl">>,<<"src/cow_ws.erl">>]}.
{<<"requirements">>,[]}.
{<<"licenses">>,[<<"ISC">>]}.

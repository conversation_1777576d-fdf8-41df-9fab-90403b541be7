# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/runtime@^7.12.5", "@babel/runtime@^7.26.7":
  "integrity" "sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.0.tgz"
  "version" "7.27.0"
  dependencies:
    "regenerator-runtime" "^0.14.0"

"@types/raf@^3.4.0":
  "integrity" "sha512-c4YAvMedbPZ5tEyxzQdMoOhhJ4RD3rngZIdwC2/qDN3d7JpEhB6fiBRKVY1lg5B7Wk+uPBjn5f39j1/2MY1oOw=="
  "resolved" "https://registry.npmjs.org/@types/raf/-/raf-3.4.3.tgz"
  "version" "3.4.3"

"@types/trusted-types@^2.0.7":
  "integrity" "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw=="
  "resolved" "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz"
  "version" "2.0.7"

"@vue/reactivity@~3.1.1":
  "integrity" "sha512-1tdfLmNjWG6t/CsPldh+foumYFo3cpyCHgBYQ34ylaMsJ+SNHQ1kApMIa8jN+i593zQuaw3AdWH0nJTARzCFhg=="
  "resolved" "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "@vue/shared" "3.1.5"

"@vue/shared@3.1.5":
  "integrity" "sha512-oJ4F3TnvpXaQwZJNF3ZK+kLPHKarDmJjJ6jyzVNDKH9md1dptjC7lWR//jrGuLdek/U6iltWxqAnYOu8gCiOvA=="
  "resolved" "https://registry.npmjs.org/@vue/shared/-/shared-3.1.5.tgz"
  "version" "3.1.5"

"alpinejs@^3.14.1":
  "integrity" "sha512-ICar8UsnRZAYvv/fCNfNeKMXNoXGUfwHrjx7LqXd08zIP95G2d9bAOuaL97re+1mgt/HojqHsfdOLo/A5LuWgQ=="
  "resolved" "https://registry.npmjs.org/alpinejs/-/alpinejs-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "@vue/reactivity" "~3.1.1"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"base64-arraybuffer@^1.0.2":
  "integrity" "sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ=="
  "resolved" "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  "version" "1.0.2"

"btoa@^1.2.1":
  "integrity" "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g=="
  "resolved" "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz"
  "version" "1.2.1"

"canvg@^3.0.11":
  "integrity" "sha512-5ON+q7jCTgMp9cjpu4Jo6XbvfYwSB2Ow3kzHKfIyJfaCAOHLbdKPQqGKgfED/R5B+3TFFfe8pegYA+b423SRyA=="
  "resolved" "https://registry.npmjs.org/canvg/-/canvg-3.0.11.tgz"
  "version" "3.0.11"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    "core-js" "^3.8.3"
    "raf" "^3.4.1"
    "regenerator-runtime" "^0.13.7"
    "rgbcolor" "^1.0.1"
    "stackblur-canvas" "^2.0.0"
    "svg-pathdata" "^6.0.3"

"core-js@^3.6.0", "core-js@^3.8.3":
  "integrity" "sha512-SJ4/EHwS36QMJd6h/Rg+GyR4A5xE0FSI3eZ+iBVpfqf1x0eTSg1smWLHrA+2jQThZSh97fmSgFSU8B61nxosxA=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.41.0.tgz"
  "version" "3.41.0"

"css-line-break@^2.1.0":
  "integrity" "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w=="
  "resolved" "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "utrie" "^1.0.2"

"dompurify@^3.2.4":
  "integrity" "sha512-mLPd29uoRe9HpvwP2TxClGQBzGXeEC/we/q+bFlmPPmj2p2Ugl3r6ATu/UU1v77DXNcehiBg9zsr1dREyA/dJQ=="
  "resolved" "https://registry.npmjs.org/dompurify/-/dompurify-3.2.5.tgz"
  "version" "3.2.5"
  optionalDependencies:
    "@types/trusted-types" "^2.0.7"

"es6-promise@^4.2.5":
  "integrity" "sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w=="
  "resolved" "https://registry.npmjs.org/es6-promise/-/es6-promise-4.2.8.tgz"
  "version" "4.2.8"

"fflate@^0.8.1":
  "integrity" "sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A=="
  "resolved" "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz"
  "version" "0.8.2"

"html2canvas@^1.0.0", "html2canvas@^1.0.0-rc.5":
  "integrity" "sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA=="
  "resolved" "https://registry.npmjs.org/html2canvas/-/html2canvas-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "css-line-break" "^2.1.0"
    "text-segmentation" "^1.0.3"

"html2pdf.js@^0.10.3":
  "integrity" "sha512-RcB1sh8rs5NT3jgbN5zvvTmkmZrsUrxpZ/RI8TMbvuReNZAdJZG5TMfA2TBP6ZXxpXlWf9NB/ciLXVb6W2LbRQ=="
  "resolved" "https://registry.npmjs.org/html2pdf.js/-/html2pdf.js-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "es6-promise" "^4.2.5"
    "html2canvas" "^1.0.0"
    "jspdf" "^3.0.0"

"jquery@^3.7.1":
  "integrity" "sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg=="
  "resolved" "https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz"
  "version" "3.7.1"

"jspdf@^3.0.0":
  "integrity" "sha512-qaGIxqxetdoNnFQQXxTKUD9/Z7AloLaw94fFsOiJMxbfYdBbrBuhWmbzI8TVjrw7s3jBY1PFHofBKMV/wZPapg=="
  "resolved" "https://registry.npmjs.org/jspdf/-/jspdf-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@babel/runtime" "^7.26.7"
    "atob" "^2.1.2"
    "btoa" "^1.2.1"
    "fflate" "^0.8.1"
  optionalDependencies:
    "canvg" "^3.0.11"
    "core-js" "^3.6.0"
    "dompurify" "^3.2.4"
    "html2canvas" "^1.0.0-rc.5"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"raf@^3.4.1":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"regenerator-runtime@^0.13.7":
  "integrity" "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"regenerator-runtime@^0.14.0":
  "integrity" "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  "version" "0.14.1"

"rgbcolor@^1.0.1":
  "integrity" "sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw=="
  "resolved" "https://registry.npmjs.org/rgbcolor/-/rgbcolor-1.0.1.tgz"
  "version" "1.0.1"

"select2@^4.1.0-rc.0":
  "integrity" "sha512-Hr9TdhyHCZUtwznEH2CBf7967mEM0idtJ5nMtjvk3Up5tPukOLXbHUNmh10oRfeNIhj+3GD3niu+g6sVK+gK0A=="
  "resolved" "https://registry.npmjs.org/select2/-/select2-4.1.0-rc.0.tgz"
  "version" "4.1.0-rc.0"

"stackblur-canvas@^2.0.0":
  "integrity" "sha512-yf7OENo23AGJhBriGx0QivY5JP6Y1HbrrDI6WLt6C5auYZXlQrheoY8hD4ibekFKz1HOfE48Ww8kMWMnJD/zcQ=="
  "resolved" "https://registry.npmjs.org/stackblur-canvas/-/stackblur-canvas-2.7.0.tgz"
  "version" "2.7.0"

"svg-pathdata@^6.0.3":
  "integrity" "sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw=="
  "resolved" "https://registry.npmjs.org/svg-pathdata/-/svg-pathdata-6.0.3.tgz"
  "version" "6.0.3"

"text-segmentation@^1.0.3":
  "integrity" "sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw=="
  "resolved" "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "utrie" "^1.0.2"

"utrie@^1.0.2":
  "integrity" "sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw=="
  "resolved" "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "base64-arraybuffer" "^1.0.2"
